import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  TextField,
  Button,
  FormControlLabel,
  Switch,
  Alert,
  InputAdornment,
  Stack,
  IconButton,
  Divider,
  ToggleButtonGroup,
  ToggleButton,
  Card,
  CardContent,
} from '@mui/material';
import {
  Keyboard,
  Edit,
  LightMode,
  DarkMode,
  Warning,
  DeleteForever,
  RestoreFromTrash,
} from '@mui/icons-material';
import { useAppSelector, useAppDispatch } from '@/store';
import {
  loadPreferences,
  savePreferences,
  setAutoStart,
  setShowInSystemTray,
  setMaxHistoryLength,
  updateShortcut,
  setTheme,
} from '@/store/slices/preferencesSlice';
import { clearHistory } from '@/store/slices/clipboardSlice';
import { showSnackbar } from '@/store/slices/uiSlice';
import { invoke } from '@tauri-apps/api/core';
import ShortcutDialog from './ShortcutDialog';

const Settings: React.FC = () => {
  const dispatch = useAppDispatch();
  const preferences = useAppSelector((state) => state.preferences);
  const { history = [] } = useAppSelector((state) => state.clipboard);
  const [shortcutDialog, setShortcutDialog] = useState<{
    open: boolean;
    key: string;
    title: string;
    currentShortcut: string;
  }>({
    open: false,
    key: '',
    title: '',
    currentShortcut: '',
  });

  // Load preferences on component mount
  useEffect(() => {
    dispatch(loadPreferences());
  }, [dispatch]);

  // Auto-save helper function
  const autoSave = async (updatedPreferences: typeof preferences) => {
    try {
      await dispatch(savePreferences(updatedPreferences)).unwrap();
      dispatch(showSnackbar({ message: 'Settings saved', severity: 'success' }));
    } catch (error) {
      console.error('Failed to auto-save preferences:', error);
      dispatch(showSnackbar({ message: 'Failed to save settings', severity: 'error' }));
    }
  };

  const handleThemeChange = async (theme: 'light' | 'dark') => {
    dispatch(setTheme(theme));
    const updatedPreferences = { ...preferences, theme };
    await autoSave(updatedPreferences);
  };

  const handleClearHistory = async () => {
    if (window.confirm('Are you sure you want to clear all clipboard history? This action cannot be undone.')) {
      try {
        await dispatch(clearHistory()).unwrap();
        dispatch(showSnackbar({ message: 'Clipboard history cleared', severity: 'success' }));
      } catch (error) {
        console.error('Failed to clear history:', error);
        dispatch(showSnackbar({ message: 'Failed to clear history', severity: 'error' }));
      }
    }
  };

  const handleResetSettings = async () => {
    if (window.confirm('Are you sure you want to reset all settings to default? This action cannot be undone.')) {
      try {
        await dispatch(loadPreferences()).unwrap();
        dispatch(showSnackbar({ message: 'Settings reset to defaults', severity: 'success' }));
      } catch (error) {
        console.error('Failed to reset settings:', error);
        dispatch(showSnackbar({ message: 'Failed to reset settings', severity: 'error' }));
      }
    }
  };

  const handleShortcutChange = async (key: string, value: string) => {
    try {
      // Update Redux store immediately
      dispatch(updateShortcut({ key: key as keyof typeof preferences.shortcuts, value }));

      // Save to backend immediately
      const updatedPreferences = {
        ...preferences,
        shortcuts: {
          ...preferences.shortcuts,
          [key]: value,
        },
      };
      await autoSave(updatedPreferences);

      // Update global shortcuts
      await invoke('update_global_shortcuts', { shortcuts: updatedPreferences.shortcuts });
    } catch (error) {
      console.error('Failed to update shortcut:', error);
    }
  };

  const handleAutoStartChange = async (enabled: boolean) => {
    try {
      // Update Redux store immediately
      dispatch(setAutoStart(enabled));

      // Apply auto-start setting immediately
      await invoke('set_auto_start', { enabled });

      // Save to backend
      const updatedPreferences = { ...preferences, autoStart: enabled };
      await autoSave(updatedPreferences);
    } catch (error) {
      console.error('Failed to update auto start setting:', error);
      // Revert on error
      dispatch(setAutoStart(!enabled));
    }
  };

  const handleSystemTrayChange = async (enabled: boolean) => {
    try {
      // Update Redux store immediately
      dispatch(setShowInSystemTray(enabled));

      // Apply system tray setting immediately
      await invoke('set_system_tray', { enabled });

      // Save to backend
      const updatedPreferences = { ...preferences, showInSystemTray: enabled };
      await autoSave(updatedPreferences);
    } catch (error) {
      console.error('Failed to update system tray setting:', error);
      // Revert on error
      dispatch(setShowInSystemTray(!enabled));
    }
  };

  const handleMaxHistoryLengthChange = async (value: number) => {
    try {
      // Update Redux store immediately
      dispatch(setMaxHistoryLength(value));

      // Save to backend
      const updatedPreferences = { ...preferences, maxHistoryLength: value };
      await autoSave(updatedPreferences);
    } catch (error) {
      console.error('Failed to update max history length:', error);
      // Revert on error
      dispatch(setMaxHistoryLength(preferences.maxHistoryLength));
    }
  };

  const openShortcutDialog = (key: string, title: string) => {
    setShortcutDialog({
      open: true,
      key,
      title,
      currentShortcut: preferences.shortcuts[key as keyof typeof preferences.shortcuts],
    });
  };

  const closeShortcutDialog = () => {
    setShortcutDialog({
      open: false,
      key: '',
      title: '',
      currentShortcut: '',
    });
  };

  const handleShortcutSave = (shortcut: string) => {
    handleShortcutChange(shortcutDialog.key, shortcut);
    closeShortcutDialog();
  };

  // Safety check to prevent undefined errors
  if (!preferences.shortcuts) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography>Loading settings...</Typography>
      </Box>
    );
  }

  return (
    <Box
      sx={{
        height: '100%',
        overflow: 'auto',
        p: 3,
      }}
    >
      <Typography variant="h5" gutterBottom>
        Settings
      </Typography>

      {/* Appearance Section */}
      <Typography variant="h6" gutterBottom sx={{ mt: 3, mb: 2 }}>
        Appearance
      </Typography>
      <Divider sx={{ mb: 3 }} />

      <Box mb={4}>
        <Typography variant="body2" color="text.secondary" gutterBottom>
          Theme
        </Typography>
        <ToggleButtonGroup
          value={preferences.theme}
          exclusive
          onChange={(_, value) => value && handleThemeChange(value)}
          size="small"
        >
          <ToggleButton value="light">
            <LightMode sx={{ mr: 1 }} />
            Light
          </ToggleButton>
          <ToggleButton value="dark">
            <DarkMode sx={{ mr: 1 }} />
            Dark
          </ToggleButton>
        </ToggleButtonGroup>
      </Box>

      {/* Behavior Section */}
      <Typography variant="h6" gutterBottom sx={{ mt: 3, mb: 2 }}>
        Behavior
      </Typography>
      <Divider sx={{ mb: 3 }} />

      <Stack spacing={3} mb={4}>
        <TextField
          type="number"
          label="Max History Length"
          value={preferences.maxHistoryLength}
          onChange={(e) => handleMaxHistoryLengthChange(parseInt(e.target.value) || 100)}
          size="small"
          helperText="Maximum number of clipboard entries to keep"
          inputProps={{ min: 10, max: 1000 }}
          sx={{ maxWidth: 300 }}
        />

        <Box>
          <FormControlLabel
            control={
              <Switch
                checked={preferences.autoStart}
                onChange={(e) => handleAutoStartChange(e.target.checked)}
              />
            }
            label="Auto Start"
          />
          <Typography variant="caption" color="text.secondary" display="block" ml={4}>
            Start the app automatically when system boots
          </Typography>
        </Box>

        <Box>
          <FormControlLabel
            control={
              <Switch
                checked={preferences.showInSystemTray}
                onChange={(e) => handleSystemTrayChange(e.target.checked)}
              />
            }
            label="Show in System Tray"
          />
          <Typography variant="caption" color="text.secondary" display="block" ml={4}>
            Keep the app running in the system tray
          </Typography>
        </Box>
      </Stack>

      {/* Shortcuts Section */}
      <Typography variant="h6" gutterBottom sx={{ mt: 3, mb: 2 }}>
        <Keyboard sx={{ mr: 1, verticalAlign: 'middle' }} />
        Shortcuts
      </Typography>
      <Divider sx={{ mb: 3 }} />

      <Stack spacing={3} mb={4}>
        <Box display="flex" gap={2} flexWrap="wrap">
          <TextField
            label="Toggle App"
            value={preferences.shortcuts.toggleApp}
            onChange={(e) => handleShortcutChange('toggleApp', e.target.value)}
            size="small"
            helperText="Shortcut to open/close the app"
            sx={{ minWidth: 250 }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Keyboard fontSize="small" />
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    size="small"
                    onClick={() => openShortcutDialog('toggleApp', 'Toggle App Shortcut')}
                    title="Record shortcut"
                  >
                    <Edit fontSize="small" />
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />

          <TextField
            label="Clear History"
            value={preferences.shortcuts.clearHistory}
            onChange={(e) => handleShortcutChange('clearHistory', e.target.value)}
            size="small"
            helperText="Shortcut to clear clipboard history"
            sx={{ minWidth: 250 }}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    size="small"
                    onClick={() => openShortcutDialog('clearHistory', 'Clear History Shortcut')}
                    title="Record shortcut"
                  >
                    <Edit fontSize="small" />
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />
        </Box>

        <Box display="flex" gap={2} flexWrap="wrap">
          <TextField
            label="Toggle Favorite"
            value={preferences.shortcuts.toggleFavorite}
            onChange={(e) => handleShortcutChange('toggleFavorite', e.target.value)}
            size="small"
            helperText="Shortcut to favorite selected item"
            sx={{ minWidth: 250 }}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    size="small"
                    onClick={() => openShortcutDialog('toggleFavorite', 'Toggle Favorite Shortcut')}
                    title="Record shortcut"
                  >
                    <Edit fontSize="small" />
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />

          <TextField
            label="Search"
            value={preferences.shortcuts.search}
            onChange={(e) => handleShortcutChange('search', e.target.value)}
            size="small"
            helperText="Shortcut to focus search"
            sx={{ minWidth: 250 }}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    size="small"
                    onClick={() => openShortcutDialog('search', 'Search Shortcut')}
                    title="Record shortcut"
                  >
                    <Edit fontSize="small" />
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />
        </Box>

        <Alert severity="info">
          <Typography variant="body2">
            <strong>Shortcut Format:</strong> Use modifiers like CommandOrControl, Shift, Alt followed by a key.
            <br />
            Examples: "CommandOrControl+Shift+V", "Alt+C", "F1"
          </Typography>
        </Alert>
      </Stack>

      {/* Danger Zone Section */}
      <Typography variant="h6" gutterBottom sx={{ mt: 4, mb: 2 }}>
        <Warning sx={{ mr: 1, verticalAlign: 'middle', color: 'error.main' }} />
        Danger Zone
      </Typography>
      <Divider sx={{ mb: 3 }} />

      <Card sx={{ border: 2, borderColor: 'error.main', mb: 4 }}>
        <CardContent>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            These actions cannot be undone. Please proceed with caution.
          </Typography>

          <Stack spacing={2} mt={2}>
            <Box>
              <Button
                variant="outlined"
                color="error"
                onClick={handleClearHistory}
                disabled={history.length === 0}
                startIcon={<DeleteForever />}
                sx={{ mb: 1 }}
              >
                Clear All Clipboard History
              </Button>
              <Typography variant="caption" color="text.secondary" display="block">
                Permanently delete all clipboard entries ({history.length} items)
              </Typography>
            </Box>

            <Box>
              <Button
                variant="outlined"
                color="error"
                onClick={handleResetSettings}
                startIcon={<RestoreFromTrash />}
                sx={{ mb: 1 }}
              >
                Reset All Settings
              </Button>
              <Typography variant="caption" color="text.secondary" display="block">
                Reset all preferences to their default values
              </Typography>
            </Box>
          </Stack>
        </CardContent>
      </Card>

      {/* Shortcut Dialog */}
      <ShortcutDialog
        open={shortcutDialog.open}
        onClose={closeShortcutDialog}
        onSave={handleShortcutSave}
        title={shortcutDialog.title}
        currentShortcut={shortcutDialog.currentShortcut}
      />
    </Box>
  );
};

export default Settings;
