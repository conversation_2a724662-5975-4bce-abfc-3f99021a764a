import { render, screen, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
import { ThemeProvider } from '@mui/material';
import { configureStore } from '@reduxjs/toolkit';
import ClipboardEntry from '@/components/ClipboardEntry';
import clipboardSlice from '@/store/slices/clipboardSlice';
import preferencesSlice from '@/store/slices/preferencesSlice';
import themeWithOverrides from '@/theme';
import type { ClipboardEntry as ClipboardEntryType } from '@/types/clipboard';

const mockStore = configureStore({
  reducer: {
    clipboard: clipboardSlice,
    preferences: preferencesSlice,
  },
});

const mockEntry: ClipboardEntryType = {
  id: '1',
  content: 'Test clipboard content',
  type: 'text',
  timestamp: Date.now(),
  isFavorite: false,
    isPinned: false,
  tags: [],
};

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <Provider store={mockStore}>
      <ThemeProvider theme={themeWithOverrides}>
        {component}
      </ThemeProvider>
    </Provider>
  );
};

describe('ClipboardEntry', () => {
  const mockOnClick = vi.fn();
  const mockOnCopy = vi.fn();

  beforeEach(() => {
    mockOnClick.mockClear();
    mockOnCopy.mockClear();
  });

  it('renders clipboard entry content', () => {
    renderWithProviders(
      <ClipboardEntry
        entry={mockEntry}
        isSelected={false}
        onClick={mockOnClick}
        onCopy={mockOnCopy}
      />
    );

    expect(screen.getByText('Test clipboard content')).toBeInTheDocument();
  });

  it('shows selected state correctly', () => {
    const { rerender } = renderWithProviders(
      <ClipboardEntry
        entry={mockEntry}
        isSelected={false}
        onClick={mockOnClick}
        onCopy={mockOnCopy}
      />
    );

    // Check that component renders
    expect(screen.getByText('Test clipboard content')).toBeInTheDocument();

    // Rerender as selected
    rerender(
      <Provider store={mockStore}>
        <ThemeProvider theme={themeWithOverrides}>
          <ClipboardEntry
            entry={mockEntry}
            isSelected={true}
            onClick={mockOnClick}
            onCopy={mockOnCopy}
          />
        </ThemeProvider>
      </Provider>
    );

    // Check that component still renders when selected
    expect(screen.getByText('Test clipboard content')).toBeInTheDocument();
  });

  it('calls onClick when entry is clicked', () => {
    renderWithProviders(
      <ClipboardEntry
        entry={mockEntry}
        isSelected={false}
        onClick={mockOnClick}
        onCopy={mockOnCopy}
      />
    );

    // Click on the ListItem instead of looking for a card
    const listItem = screen.getByText('Test clipboard content').closest('.MuiListItem-root');
    fireEvent.click(listItem!);

    expect(mockOnClick).toHaveBeenCalledTimes(1);
  });

  it('calls onCopy when copy button is clicked', () => {
    renderWithProviders(
      <ClipboardEntry
        entry={mockEntry}
        isSelected={false}
        onClick={mockOnClick}
        onCopy={mockOnCopy}
      />
    );

    // First hover over the list item to show the copy button
    const listItem = screen.getByText('Test clipboard content').closest('.MuiListItem-root');
    fireEvent.mouseEnter(listItem!);

    // Now find and click the copy button
    const copyButton = screen.getByRole('button', { name: /copy to clipboard/i });
    fireEvent.click(copyButton);

    expect(mockOnCopy).toHaveBeenCalledTimes(1);
    expect(mockOnClick).not.toHaveBeenCalled();
  });

  it('displays favorite status correctly', () => {
    const favoriteEntry = { ...mockEntry, isFavorite: true };
    
    renderWithProviders(
      <ClipboardEntry
        entry={favoriteEntry}
        isSelected={false}
        onClick={mockOnClick}
        onCopy={mockOnCopy}
      />
    );

    expect(screen.getByLabelText('Remove from favorites')).toBeInTheDocument();
  });
});
