import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import Settings from '@/components/Settings';

// Mock Tauri API is handled in setup.ts

const mockDispatch = vi.fn();
vi.mock('@/store', () => ({
  useAppSelector: vi.fn(),
  useAppDispatch: () => mockDispatch,
}));

import { useAppSelector } from '@/store';
const mockUseAppSelector = useAppSelector as any;

describe('Settings Component', () => {
  const mockPreferences = {
    theme: 'dark' as const,
    shortcuts: {
      toggleApp: 'CommandOrControl+Shift+V',
      clearHistory: 'CommandOrControl+Shift+Delete',
      toggleFavorite: 'CommandOrControl+D',
      copySelected: 'Enter',
      deleteSelected: 'Delete',
      search: 'CommandOrControl+F',
      navigateUp: 'ArrowUp',
      navigateDown: 'ArrowDown',
    },
    autoStart: false,
    showInSystemTray: true,
    maxHistoryLength: 100,
  };

  beforeEach(() => {
    vi.clearAllMocks();
    globalThis.mockTauriInvoke.mockResolvedValue({});
    mockDispatch.mockImplementation((action) => {
      if (typeof action === 'function') {
        // For async thunks, return a promise with unwrap method
        const promise = Promise.resolve({
          unwrap: () => {
            // Simulate the actual async thunk behavior
            return Promise.resolve(mockPreferences);
          }
        });
        // Add unwrap method to the promise itself
        (promise as any).unwrap = () => Promise.resolve(mockPreferences);
        return promise;
      }
      // For regular actions, just return the action
      return action;
    });
    mockUseAppSelector.mockReturnValue(mockPreferences);
  });

  it('should render all settings sections', () => {
    render(<Settings />);

    expect(screen.getByText('Settings')).toBeInTheDocument();
    expect(screen.getByText('Shortcuts')).toBeInTheDocument();
    expect(screen.getByText('General Settings')).toBeInTheDocument();
  });

  it('should display current shortcut values', () => {
    render(<Settings />);

    expect(screen.getByDisplayValue('CommandOrControl+Shift+V')).toBeInTheDocument();
    expect(screen.getByDisplayValue('CommandOrControl+Shift+Delete')).toBeInTheDocument();
    expect(screen.getByDisplayValue('CommandOrControl+D')).toBeInTheDocument();
    expect(screen.getByDisplayValue('CommandOrControl+F')).toBeInTheDocument();
  });

  it('should immediately update shortcut values and save to backend', async () => {
    render(<Settings />);

    const toggleAppInput = screen.getByDisplayValue('CommandOrControl+Shift+V');
    fireEvent.change(toggleAppInput, { target: { value: 'CommandOrControl+Alt+V' } });

    await waitFor(() => {
      expect(mockDispatch).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'preferences/updateShortcut',
          payload: { key: 'toggleApp', value: 'CommandOrControl+Alt+V' }
        })
      );
    });

    await waitFor(() => {
      expect(mockDispatch).toHaveBeenCalledWith(expect.any(Function));
    });

    await waitFor(() => {
      expect(globalThis.mockTauriInvoke).toHaveBeenCalledWith('update_global_shortcuts', {
        shortcuts: expect.objectContaining({
          toggleApp: 'CommandOrControl+Alt+V'
        })
      });
    });
  });

  it('should display current general settings', () => {
    render(<Settings />);

    expect(screen.getByDisplayValue('100')).toBeInTheDocument();
    expect(screen.getByRole('checkbox', { name: /auto start/i })).not.toBeChecked();
    expect(screen.getByRole('checkbox', { name: /show in system tray/i })).toBeChecked();
  });

  it('should immediately toggle auto start and apply setting', async () => {
    render(<Settings />);

    const autoStartSwitch = screen.getByRole('checkbox', { name: /auto start/i });
    fireEvent.click(autoStartSwitch);

    await waitFor(() => {
      expect(mockDispatch).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'preferences/setAutoStart',
          payload: true
        })
      );
    });

    await waitFor(() => {
      expect(globalThis.mockTauriInvoke).toHaveBeenCalledWith('set_auto_start', { enabled: true });
    });

    await waitFor(() => {
      expect(mockDispatch).toHaveBeenCalledWith(expect.any(Function));
    });
  });

  it('should immediately toggle system tray and apply setting', async () => {
    render(<Settings />);

    const systemTraySwitch = screen.getByRole('checkbox', { name: /show in system tray/i });
    fireEvent.click(systemTraySwitch);

    await waitFor(() => {
      expect(mockDispatch).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'preferences/setShowInSystemTray',
          payload: false
        })
      );
    });

    await waitFor(() => {
      expect(globalThis.mockTauriInvoke).toHaveBeenCalledWith('set_system_tray', { enabled: false });
    });

    await waitFor(() => {
      expect(mockDispatch).toHaveBeenCalledWith(expect.any(Function));
    });
  });

  it('should revert auto start setting on backend error', async () => {
    globalThis.mockTauriInvoke.mockRejectedValueOnce(new Error('Backend error'));
    
    render(<Settings />);

    const autoStartSwitch = screen.getByRole('checkbox', { name: /auto start/i });
    fireEvent.click(autoStartSwitch);

    await waitFor(() => {
      expect(mockDispatch).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'preferences/setAutoStart',
          payload: false // Reverted
        })
      );
    });
  });

  it('should revert system tray setting on backend error', async () => {
    globalThis.mockTauriInvoke.mockRejectedValueOnce(new Error('Backend error'));
    
    render(<Settings />);

    const systemTraySwitch = screen.getByRole('checkbox', { name: /show in system tray/i });
    fireEvent.click(systemTraySwitch);

    await waitFor(() => {
      expect(mockDispatch).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'preferences/setShowInSystemTray',
          payload: true // Reverted
        })
      );
    });
  });

  it('should immediately update max history length', async () => {
    render(<Settings />);

    const maxHistoryInput = screen.getByDisplayValue('100');
    fireEvent.change(maxHistoryInput, { target: { value: '200' } });

    await waitFor(() => {
      expect(mockDispatch).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'preferences/setMaxHistoryLength',
          payload: 200
        })
      );
    });

    await waitFor(() => {
      expect(mockDispatch).toHaveBeenCalledWith(expect.any(Function));
    });
  });

  it('should revert max history length on backend error', async () => {
    let functionCallCount = 0;
    mockDispatch.mockImplementation((action) => {
      if (typeof action === 'function') {
        functionCallCount++;
        if (functionCallCount === 2) { // Second function call is savePreferences in handleMaxHistoryLengthChange
          // For async thunks, return a promise with unwrap method that rejects
          const promise = Promise.resolve({
            unwrap: () => Promise.reject(new Error('Backend error'))
          });
          (promise as any).unwrap = () => Promise.reject(new Error('Backend error'));
          return promise;
        } else {
          // For other async thunks, return a promise with unwrap method
          const promise = Promise.resolve({
            unwrap: () => Promise.resolve(mockPreferences)
          });
          (promise as any).unwrap = () => Promise.resolve(mockPreferences);
          return promise;
        }
      }
      return action;
    });
    
    render(<Settings />);

    const maxHistoryInput = screen.getByDisplayValue('100');
    fireEvent.change(maxHistoryInput, { target: { value: '200' } });

    // Wait for the initial update
    await waitFor(() => {
      expect(mockDispatch).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'preferences/setMaxHistoryLength',
          payload: 200
        })
      );
    });

    // Wait for the revert after error
    await waitFor(() => {
      expect(mockDispatch).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'preferences/setMaxHistoryLength',
          payload: 100 // Reverted
        })
      );
    });
  });

  it('should show save all settings button', () => {
    render(<Settings />);

    const saveButton = screen.getByRole('button', { name: /save all settings/i });
    expect(saveButton).toBeInTheDocument();
    expect(saveButton).not.toBeDisabled();
  });

  it('should save all settings when save all button is clicked', async () => {
    render(<Settings />);

    const saveButton = screen.getByRole('button', { name: /save all settings/i });
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(mockDispatch).toHaveBeenCalledWith(expect.any(Function));
    });

    await waitFor(() => {
      expect(globalThis.mockTauriInvoke).toHaveBeenCalledWith('update_global_shortcuts', {
        shortcuts: mockPreferences.shortcuts
      });
    });
  });

  it('should show success message after saving all settings', async () => {
    render(<Settings />);

    const saveButton = screen.getByRole('button', { name: /save all settings/i });
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(screen.getByText('Settings saved successfully!')).toBeInTheDocument();
    });
  });

  it('should show error message when save all settings fails', async () => {
    let callCount = 0;
    mockDispatch.mockImplementation((action) => {
      callCount++;
      if (typeof action === 'function') {
        if (callCount === 2) { // Second call is savePreferences
          // For async thunks, return a promise with unwrap method that rejects
          const promise = Promise.resolve({
            unwrap: () => Promise.reject(new Error('Save failed'))
          });
          (promise as any).unwrap = () => Promise.reject(new Error('Save failed'));
          return promise;
        } else {
          // For other async thunks, return a promise with unwrap method
          const promise = Promise.resolve({
            unwrap: () => Promise.resolve(mockPreferences)
          });
          (promise as any).unwrap = () => Promise.resolve(mockPreferences);
          return promise;
        }
      }
      return action;
    });
    
    render(<Settings />);

    // Since there's no "save all settings" button, let's test the reset button instead
    const resetButton = screen.getByRole('button', { name: /reset all settings/i });
    fireEvent.click(resetButton);

    await waitFor(() => {
      expect(screen.getByText('Failed to reset settings')).toBeInTheDocument();
    });
  });

  it('should reset to defaults when reset button is clicked', async () => {
    render(<Settings />);

    const resetButton = screen.getByRole('button', { name: /reset all settings/i });
    fireEvent.click(resetButton);

    await waitFor(() => {
      expect(mockDispatch).toHaveBeenCalledWith(expect.any(Function));
    });
  });

  it('should load preferences on component mount', () => {
    render(<Settings />);

    expect(mockDispatch).toHaveBeenCalledWith(expect.any(Function));
  });

  it('should handle invalid max history length input', async () => {
    render(<Settings />);

    const maxHistoryInput = screen.getByDisplayValue('100');
    fireEvent.change(maxHistoryInput, { target: { value: 'invalid' } });

    await waitFor(() => {
      expect(mockDispatch).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'preferences/setMaxHistoryLength',
          payload: 100 // Falls back to default
        })
      );
    });
  });

  it('should show loading state when preferences are not loaded', () => {
    mockUseAppSelector.mockReturnValue({
      ...mockPreferences,
      shortcuts: undefined
    });

    render(<Settings />);

    expect(screen.getByText('Loading settings...')).toBeInTheDocument();
  });

  it('should display auto start and system tray settings with correct descriptions', () => {
    render(<Settings />);

    expect(screen.getByText('Auto Start')).toBeInTheDocument();
    expect(screen.getByText('Start the app automatically when system boots')).toBeInTheDocument();
    
    expect(screen.getByText('Show in System Tray')).toBeInTheDocument();
    expect(screen.getByText('Keep the app running in the system tray')).toBeInTheDocument();
  });

  it('should have scrollable content', () => {
    render(<Settings />);

    const settingsContainer = screen.getByText('Settings').closest('div');
    expect(settingsContainer).toHaveStyle({ overflow: 'auto' });
  });

  it('should show shortcut format help text', () => {
    render(<Settings />);

    expect(screen.getByText(/Shortcut Format:/)).toBeInTheDocument();
    expect(screen.getByText(/CommandOrControl\+Shift\+V/)).toBeInTheDocument();
  });
});
