import { describe, it, expect, vi, beforeEach } from 'vitest';

// Mock the Tauri API - must be hoisted before imports
vi.mock('@tauri-apps/api/core', () => ({
  invoke: vi.fn(),
}));

import { invoke } from '@tauri-apps/api/core';
import { store } from '@/store';

const mockInvoke = invoke as ReturnType<typeof vi.fn>;
import { loadPreferences, savePreferences } from '@/store/slices/preferencesSlice';
import { getClipboardHistory, saveClipboardEntry, toggleFavorite, clearHistory, deleteEntry } from '@/store/slices/clipboardSlice';
import type { PreferencesState, ClipboardEntry } from '@/types/clipboard';

describe('Data Persistence', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Preferences Persistence', () => {
    it('loads preferences from backend', async () => {
      const mockPreferences: PreferencesState = {
        theme: 'dark',
        shortcuts: {
          toggleApp: 'CommandOrControl+Shift+V',
          clearHistory: 'CommandOrControl+Shift+Delete',
          toggleFavorite: 'CommandOrControl+D',
          copySelected: 'Enter',
          deleteSelected: 'Delete',
          search: 'CommandOrControl+F',
          navigateUp: 'ArrowUp',
          navigateDown: 'ArrowDown',
        },
        autoStart: true,
        showInSystemTray: true,
        maxHistoryLength: 150,
      };

      mockInvoke.mockResolvedValueOnce(mockPreferences);

      const result = await store.dispatch(loadPreferences());
      
      expect(mockInvoke).toHaveBeenCalledWith('get_preferences');
      expect(result.payload).toEqual(mockPreferences);
      expect(store.getState().preferences).toEqual(mockPreferences);
    });

    it('saves preferences to backend', async () => {
      const preferencesToSave: Partial<PreferencesState> = {
        maxHistoryLength: 200,
        autoStart: true,
      };

      const updatedPreferences: PreferencesState = {
        theme: 'dark',
        shortcuts: {
          toggleApp: 'CommandOrControl+Shift+V',
          clearHistory: 'CommandOrControl+Shift+Delete',
          toggleFavorite: 'CommandOrControl+D',
          copySelected: 'Enter',
          deleteSelected: 'Delete',
          search: 'CommandOrControl+F',
          navigateUp: 'ArrowUp',
          navigateDown: 'ArrowDown',
        },
        autoStart: true,
        showInSystemTray: true,
        maxHistoryLength: 200,
      };

      mockInvoke.mockResolvedValueOnce(updatedPreferences);

      const result = await store.dispatch(savePreferences(preferencesToSave));
      
      expect(mockInvoke).toHaveBeenCalledWith('save_preferences', { preferences: preferencesToSave });
      expect(result.payload).toEqual(updatedPreferences);
      expect(store.getState().preferences).toEqual(updatedPreferences);
    });

    it('handles preferences loading errors', async () => {
      const error = new Error('Failed to load preferences');
      mockInvoke.mockRejectedValueOnce(error);

      const result = await store.dispatch(loadPreferences());
      
      expect(result.type).toBe('preferences/load/rejected');
      if (result.type === 'preferences/load/rejected') {
        expect((result as any).error.message).toBe('Failed to load preferences');
      }
    });

    it('handles preferences saving errors', async () => {
      const error = new Error('Failed to save preferences');
      mockInvoke.mockRejectedValueOnce(error);

      const result = await store.dispatch(savePreferences({ maxHistoryLength: 300 }));
      
      expect(result.type).toBe('preferences/save/rejected');
      if (result.type === 'preferences/save/rejected') {
        expect((result as any).error.message).toBe('Failed to save preferences');
      }
    });
  });

  describe('Clipboard History Persistence', () => {
    it('loads clipboard history from backend', async () => {
      const mockHistory: ClipboardEntry[] = [
        {
          id: '1',
          content: 'Test content 1',
          type: 'text',
          timestamp: Date.now(),
          isFavorite: false,
    isPinned: false,
          tags: [],
        },
        {
          id: '2',
          content: 'Test content 2',
          type: 'text',
          timestamp: Date.now() - 1000,
          isFavorite: true,
    isPinned: false,
          tags: [],
        },
      ];

      mockInvoke.mockResolvedValueOnce(mockHistory);

      const result = await store.dispatch(getClipboardHistory());
      
      expect(mockInvoke).toHaveBeenCalledWith('get_clipboard_history');
      expect(result.payload).toEqual(mockHistory);
      expect(store.getState().clipboard.history).toEqual(mockHistory);
      expect(store.getState().clipboard.favorites).toEqual([mockHistory[1]]);
    });

    it('saves clipboard entry to backend', async () => {
      const entryToSave = {
        content: 'New clipboard content',
        type: 'text' as const,
        isFavorite: false,
        isPinned: false,
        tags: [],
      };

      const savedEntry: ClipboardEntry = {
        id: '3',
        content: 'New clipboard content',
        type: 'text',
        timestamp: Date.now(),
        isFavorite: false,
    isPinned: false,
        tags: [],
      };

      mockInvoke.mockResolvedValueOnce(savedEntry);

      const result = await store.dispatch(saveClipboardEntry(entryToSave));
      
      expect(mockInvoke).toHaveBeenCalledWith('save_clipboard_entry', { entry: entryToSave });
      expect(result.payload).toEqual(savedEntry);
    });

    it('toggles favorite status and persists to backend', async () => {
      const entryId = '1';
      const updatedEntry: ClipboardEntry = {
        id: '1',
        content: 'Test content 1',
        type: 'text',
        timestamp: Date.now(),
        isFavorite: true,
    isPinned: false,
        tags: [],
      };

      mockInvoke.mockResolvedValueOnce(updatedEntry);

      const result = await store.dispatch(toggleFavorite(entryId));
      
      expect(mockInvoke).toHaveBeenCalledWith('toggle_favorite', { entryId });
      expect(result.payload).toEqual(updatedEntry);
    });

    it('deletes entry from backend', async () => {
      const entryId = '1';

      mockInvoke.mockResolvedValueOnce(undefined);

      const result = await store.dispatch(deleteEntry(entryId));
      
      expect(mockInvoke).toHaveBeenCalledWith('delete_clipboard_entry', { entryId });
      expect(result.payload).toBe(entryId);
    });

    it('clears history in backend', async () => {
      mockInvoke.mockResolvedValueOnce(undefined);

      const result = await store.dispatch(clearHistory());
      
      expect(mockInvoke).toHaveBeenCalledWith('clear_clipboard_history');
      expect(result.type).toBe('clipboard/clearHistory/fulfilled');
    });

    it('handles clipboard history loading errors', async () => {
      const error = new Error('Failed to load history');
      mockInvoke.mockRejectedValueOnce(error);

      const result = await store.dispatch(getClipboardHistory());
      
      expect(result.type).toBe('clipboard/getHistory/rejected');
      if (result.type === 'clipboard/getHistory/rejected') {
        expect((result as any).error.message).toBe('Failed to load history');
      }
    });
  });

  describe('Max History Length Enforcement', () => {
    it('syncs max history length from preferences to clipboard state', async () => {
      const mockPreferences: PreferencesState = {
        theme: 'dark',
        shortcuts: {
          toggleApp: 'CommandOrControl+Shift+V',
          clearHistory: 'CommandOrControl+Shift+Delete',
          toggleFavorite: 'CommandOrControl+D',
          copySelected: 'Enter',
          deleteSelected: 'Delete',
          search: 'CommandOrControl+F',
          navigateUp: 'ArrowUp',
          navigateDown: 'ArrowDown',
        },
        autoStart: false,
        showInSystemTray: true,
        maxHistoryLength: 50,
      };

      mockInvoke.mockResolvedValueOnce(mockPreferences);

      await store.dispatch(loadPreferences());
      
      // Check that clipboard state was updated with the new max history length
      expect(store.getState().clipboard.maxHistoryLength).toBe(50);
    });

    it('syncs max history length when preferences are saved', async () => {
      const updatedPreferences: PreferencesState = {
        theme: 'dark',
        shortcuts: {
          toggleApp: 'CommandOrControl+Shift+V',
          clearHistory: 'CommandOrControl+Shift+Delete',
          toggleFavorite: 'CommandOrControl+D',
          copySelected: 'Enter',
          deleteSelected: 'Delete',
          search: 'CommandOrControl+F',
          navigateUp: 'ArrowUp',
          navigateDown: 'ArrowDown',
        },
        autoStart: false,
        showInSystemTray: true,
        maxHistoryLength: 75,
      };

      mockInvoke.mockResolvedValueOnce(updatedPreferences);

      await store.dispatch(savePreferences({ maxHistoryLength: 75 }));
      
      // Check that clipboard state was updated with the new max history length
      expect(store.getState().clipboard.maxHistoryLength).toBe(75);
    });
  });

  describe('Auto-start and System Tray Persistence', () => {
    it('saves auto-start preference to backend', async () => {
      const preferencesToSave: Partial<PreferencesState> = {
        autoStart: true,
      };

      const updatedPreferences: PreferencesState = {
        theme: 'dark',
        shortcuts: {
          toggleApp: 'CommandOrControl+Shift+V',
          clearHistory: 'CommandOrControl+Shift+Delete',
          toggleFavorite: 'CommandOrControl+D',
          copySelected: 'Enter',
          deleteSelected: 'Delete',
          search: 'CommandOrControl+F',
          navigateUp: 'ArrowUp',
          navigateDown: 'ArrowDown',
        },
        autoStart: true,
        showInSystemTray: true,
        maxHistoryLength: 100,
      };

      mockInvoke.mockResolvedValueOnce(updatedPreferences);

      const result = await store.dispatch(savePreferences(preferencesToSave));
      
      expect(mockInvoke).toHaveBeenCalledWith('save_preferences', { preferences: preferencesToSave });
      expect(result.payload).toEqual(updatedPreferences);
      expect(store.getState().preferences.autoStart).toBe(true);
    });

    it('saves system tray preference to backend', async () => {
      const preferencesToSave: Partial<PreferencesState> = {
        showInSystemTray: false,
      };

      const updatedPreferences: PreferencesState = {
        theme: 'dark',
        shortcuts: {
          toggleApp: 'CommandOrControl+Shift+V',
          clearHistory: 'CommandOrControl+Shift+Delete',
          toggleFavorite: 'CommandOrControl+D',
          copySelected: 'Enter',
          deleteSelected: 'Delete',
          search: 'CommandOrControl+F',
          navigateUp: 'ArrowUp',
          navigateDown: 'ArrowDown',
        },
        autoStart: false,
        showInSystemTray: false,
        maxHistoryLength: 100,
      };

      mockInvoke.mockResolvedValueOnce(updatedPreferences);

      const result = await store.dispatch(savePreferences(preferencesToSave));
      
      expect(mockInvoke).toHaveBeenCalledWith('save_preferences', { preferences: preferencesToSave });
      expect(result.payload).toEqual(updatedPreferences);
      expect(store.getState().preferences.showInSystemTray).toBe(false);
    });
  });

  describe('Backend Integration', () => {
    it('calls correct backend commands for all operations', async () => {
      // Test all the different backend calls
      const operations = [
        { action: () => store.dispatch(getClipboardHistory()), expectedCall: 'get_clipboard_history', hasArgs: false },
        { action: () => store.dispatch(loadPreferences()), expectedCall: 'get_preferences', hasArgs: false },
        { action: () => store.dispatch(clearHistory()), expectedCall: 'clear_clipboard_history', hasArgs: false },
      ];

      for (const { action, expectedCall, hasArgs } of operations) {
        mockInvoke.mockClear();
        mockInvoke.mockResolvedValueOnce({});
        
        await action();
        
        if (hasArgs) {
          expect(mockInvoke).toHaveBeenCalledWith(expectedCall, expect.anything());
        } else {
          expect(mockInvoke).toHaveBeenCalledWith(expectedCall);
        }
      }
    });

    it('handles network/backend errors gracefully', async () => {
      const networkError = new Error('Network error');
      mockInvoke.mockRejectedValue(networkError);

      const operations = [
        () => store.dispatch(getClipboardHistory()),
        () => store.dispatch(loadPreferences()),
        () => store.dispatch(savePreferences({})),
        () => store.dispatch(toggleFavorite('test-id')),
        () => store.dispatch(deleteEntry('test-id')),
        () => store.dispatch(clearHistory()),
      ];

      for (const operation of operations) {
        const result = await operation();
        expect(result.type).toMatch(/\/rejected$/);
      }
    });
  });
});
