import { render, screen, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import ClipboardHistoryList from '@/components/ClipboardHistoryList';
import clipboardSlice from '@/store/slices/clipboardSlice';
import preferencesSlice from '@/store/slices/preferencesSlice';
import type { ClipboardEntry } from '@/types/clipboard';

// Mock the useKeyboardShortcuts hook
vi.mock('@/hooks/useKeyboardShortcuts', () => ({
  useKeyboardShortcuts: () => ({
    selectedEntryId: null,
  }),
}));

const createMockStore = (initialState: any = {}) => {
  return configureStore({
    reducer: {
      clipboard: clipboardSlice,
      preferences: preferencesSlice,
    },
    preloadedState: {
      clipboard: {
        history: [],
        favorites: [],
        maxHistoryLength: 100,
        isMonitoring: false,
        searchQuery: '',
        selectedEntryId: null,
        selectedTags: [],
        ...initialState.clipboard,
      },
      preferences: {
        theme: 'dark',
        shortcuts: {
          toggleApp: 'CommandOrControl+Shift+V',
          clearHistory: 'CommandOrControl+Shift+Delete',
          toggleFavorite: 'CommandOrControl+D',
          copySelected: 'Enter',
          deleteSelected: 'Delete',
          search: 'CommandOrControl+F',
          navigateUp: 'ArrowUp',
          navigateDown: 'ArrowDown',
        },
        autoStart: false,
        showInSystemTray: true,
        maxHistoryLength: 100,
        ...initialState.preferences,
      },
    },
  });
};

const mockEntries: ClipboardEntry[] = [
  {
    id: '1',
    content: 'First entry content',
    type: 'text',
    timestamp: Date.now() - 1000,
    isFavorite: false,
    isPinned: false,
    name: 'First Entry',
    tags: ['work', 'important'],
  },
  {
    id: '2',
    content: 'Second entry content',
    type: 'text',
    timestamp: Date.now() - 2000,
    isFavorite: true,
    isPinned: false,
    name: 'Second Entry',
    tags: ['personal', 'notes'],
  },
  {
    id: '3',
    content: 'Third entry content',
    type: 'text',
    timestamp: Date.now() - 3000,
    isFavorite: false,
    isPinned: false,
    name: undefined,
    tags: ['work', 'meeting'],
  },
  {
    id: '4',
    content: 'Fourth entry content',
    type: 'text',
    timestamp: Date.now() - 4000,
    isFavorite: true,
    isPinned: false,
    name: 'Fourth Entry',
    tags: ['important', 'urgent'],
  },
];

describe('ClipboardHistoryList Enhanced Features', () => {
  describe('Tag Filter', () => {
    it('should not display separate tag filter (now handled via smart search)', () => {
      const store = createMockStore({
        clipboard: {
          history: mockEntries,
        },
      });

      render(
        <Provider store={store}>
          <ClipboardHistoryList showFavoritesOnly={false} />
        </Provider>
      );

      // Tag filtering is now handled via smart search, not a separate filter
      expect(screen.queryByLabelText('Filter by Tags')).not.toBeInTheDocument();
    });

    it('should not display tag filter when no entries have tags', () => {
      const entriesWithoutTags = mockEntries.map(entry => ({ ...entry, tags: [] }));
      const store = createMockStore({
        clipboard: {
          history: entriesWithoutTags,
        },
      });

      render(
        <Provider store={store}>
          <ClipboardHistoryList showFavoritesOnly={false} />
        </Provider>
      );

      expect(screen.queryByLabelText('Filter by Tags')).not.toBeInTheDocument();
    });

    it('should show all unique tags in entry chips', () => {
      const store = createMockStore({
        clipboard: {
          history: mockEntries,
        },
      });

      render(
        <Provider store={store}>
          <ClipboardHistoryList showFavoritesOnly={false} />
        </Provider>
      );

      // Check that all unique tags are present as chips in entries
      expect(screen.getAllByText('important')).toHaveLength(1);
      expect(screen.getAllByText('meeting')).toHaveLength(1);
      expect(screen.getAllByText('notes')).toHaveLength(1);
      expect(screen.getAllByText('personal')).toHaveLength(1);
      expect(screen.getAllByText('urgent')).toHaveLength(1);
      expect(screen.getAllByText('work')).toHaveLength(1);
    });

    it('should filter entries by selected tags', () => {
      const store = createMockStore({
        clipboard: {
          history: mockEntries,
          selectedTags: ['work'],
        },
      });

      render(
        <Provider store={store}>
          <ClipboardHistoryList showFavoritesOnly={false} />
        </Provider>
      );

      // Should show entries with 'work' tag
      expect(screen.getByText('First entry content')).toBeInTheDocument();
      expect(screen.getByText('Third entry content')).toBeInTheDocument();
      
      // Should not show entries without 'work' tag
      expect(screen.queryByText('Second entry content')).not.toBeInTheDocument();
      expect(screen.queryByText('Fourth entry content')).not.toBeInTheDocument();
    });

    it('should show entries that match any of the selected tags', () => {
      const store = createMockStore({
        clipboard: {
          history: mockEntries,
          selectedTags: ['work', 'personal'],
        },
      });

      render(
        <Provider store={store}>
          <ClipboardHistoryList showFavoritesOnly={false} />
        </Provider>
      );

      // Should show entries with either 'work' or 'personal' tags
      expect(screen.getByText('First entry content')).toBeInTheDocument(); // has 'work'
      expect(screen.getByText('Second entry content')).toBeInTheDocument(); // has 'personal'
      expect(screen.getByText('Third entry content')).toBeInTheDocument(); // has 'work'
      
      // Should not show entry with neither tag
      expect(screen.queryByText('Fourth entry content')).not.toBeInTheDocument();
    });

    it('should display selected tags as chips in entries', () => {
      const store = createMockStore({
        clipboard: {
          history: mockEntries,
          selectedTags: ['work', 'important'],
        },
      });

      render(
        <Provider store={store}>
          <ClipboardHistoryList showFavoritesOnly={false} />
        </Provider>
      );

      // Check that tags are displayed as chips in entries (not in a separate filter)
      expect(screen.getAllByText('work')).toHaveLength(1);
      expect(screen.getAllByText('important')).toHaveLength(1);
    });
  });

  describe('Enhanced Search', () => {
    it('should search by entry name', () => {
      const store = createMockStore({
        clipboard: {
          history: mockEntries,
          searchQuery: 'First Entry',
        },
      });

      render(
        <Provider store={store}>
          <ClipboardHistoryList showFavoritesOnly={false} />
        </Provider>
      );

      expect(screen.getByText('First entry content')).toBeInTheDocument();
      expect(screen.queryByText('Second entry content')).not.toBeInTheDocument();
      expect(screen.queryByText('Third entry content')).not.toBeInTheDocument();
      expect(screen.queryByText('Fourth entry content')).not.toBeInTheDocument();
    });

    it('should search by tags', () => {
      const store = createMockStore({
        clipboard: {
          history: mockEntries,
          searchQuery: 'urgent',
        },
      });

      render(
        <Provider store={store}>
          <ClipboardHistoryList showFavoritesOnly={false} />
        </Provider>
      );

      expect(screen.getByText('Fourth entry content')).toBeInTheDocument();
      expect(screen.queryByText('First entry content')).not.toBeInTheDocument();
      expect(screen.queryByText('Second entry content')).not.toBeInTheDocument();
      expect(screen.queryByText('Third entry content')).not.toBeInTheDocument();
    });

    it('should search by content', () => {
      const store = createMockStore({
        clipboard: {
          history: mockEntries,
          searchQuery: 'Second entry',
        },
      });

      render(
        <Provider store={store}>
          <ClipboardHistoryList showFavoritesOnly={false} />
        </Provider>
      );

      expect(screen.getByText('Second entry content')).toBeInTheDocument();
      expect(screen.queryByText('First entry content')).not.toBeInTheDocument();
      expect(screen.queryByText('Third entry content')).not.toBeInTheDocument();
      expect(screen.queryByText('Fourth entry content')).not.toBeInTheDocument();
    });

    it('should be case insensitive', () => {
      const store = createMockStore({
        clipboard: {
          history: mockEntries,
          searchQuery: 'FIRST ENTRY',
        },
      });

      render(
        <Provider store={store}>
          <ClipboardHistoryList showFavoritesOnly={false} />
        </Provider>
      );

      expect(screen.getByText('First entry content')).toBeInTheDocument();
    });

    it('should combine search and tag filtering', () => {
      const store = createMockStore({
        clipboard: {
          history: mockEntries,
          searchQuery: 'entry',
          selectedTags: ['work'],
        },
      });

      render(
        <Provider store={store}>
          <ClipboardHistoryList showFavoritesOnly={false} />
        </Provider>
      );

      // Should show entries that match search AND have selected tags
      expect(screen.getByText('First entry content')).toBeInTheDocument(); // has 'work' and matches 'entry'
      expect(screen.getByText('Third entry content')).toBeInTheDocument(); // has 'work' and matches 'entry'
      
      // Should not show entries that don't have the tag, even if they match search
      expect(screen.queryByText('Second entry content')).not.toBeInTheDocument();
      expect(screen.queryByText('Fourth entry content')).not.toBeInTheDocument();
    });
  });

  describe('Results Display', () => {
    it('should not show item count when no search is active', () => {
      const store = createMockStore({
        clipboard: {
          history: mockEntries,
        },
      });

      render(
        <Provider store={store}>
          <ClipboardHistoryList showFavoritesOnly={false} />
        </Provider>
      );

      // Item count is only shown when there's a search query or filtering
      expect(screen.queryByText('4 items')).not.toBeInTheDocument();
    });

    it('should not show item count when no search is active (single item)', () => {
      const store = createMockStore({
        clipboard: {
          history: [mockEntries[0]],
        },
      });

      render(
        <Provider store={store}>
          <ClipboardHistoryList showFavoritesOnly={false} />
        </Provider>
      );

      // Item count is only shown when there's a search query or filtering
      expect(screen.queryByText('1 item')).not.toBeInTheDocument();
    });

    it('should show filtered count when search is active', () => {
      const store = createMockStore({
        clipboard: {
          history: mockEntries,
          searchQuery: 'First',
        },
      });

      render(
        <Provider store={store}>
          <ClipboardHistoryList showFavoritesOnly={false} />
        </Provider>
      );

      expect(screen.getByText('1 item')).toBeInTheDocument();
      expect(screen.getByText('filtered by "First"')).toBeInTheDocument();
    });

    it('should show no results message when no entries match filters', () => {
      const store = createMockStore({
        clipboard: {
          history: mockEntries,
          searchQuery: 'nonexistent',
        },
      });

      render(
        <Provider store={store}>
          <ClipboardHistoryList showFavoritesOnly={false} />
        </Provider>
      );

      expect(screen.getByText('No entries match your search criteria.')).toBeInTheDocument();
    });
  });

  describe('Favorites Mode', () => {
    it('should show only favorites when showFavoritesOnly is true', () => {
      const store = createMockStore({
        clipboard: {
          favorites: mockEntries.filter(entry => entry.isFavorite),
        },
      });

      render(
        <Provider store={store}>
          <ClipboardHistoryList showFavoritesOnly={true} />
        </Provider>
      );

      expect(screen.getByText('Second entry content')).toBeInTheDocument();
      expect(screen.getByText('Fourth entry content')).toBeInTheDocument();
      expect(screen.queryByText('First entry content')).not.toBeInTheDocument();
      expect(screen.queryByText('Third entry content')).not.toBeInTheDocument();
    });

    it('should filter favorites by tags', () => {
      const favorites = mockEntries.filter(entry => entry.isFavorite);
      const store = createMockStore({
        clipboard: {
          favorites,
          selectedTags: ['important'],
        },
      });

      render(
        <Provider store={store}>
          <ClipboardHistoryList showFavoritesOnly={true} />
        </Provider>
      );

      expect(screen.getByText('Fourth entry content')).toBeInTheDocument();
      expect(screen.queryByText('Second entry content')).not.toBeInTheDocument();
    });

    it('should show correct placeholder for empty favorites', () => {
      const store = createMockStore({
        clipboard: {
          favorites: [],
        },
      });

      render(
        <Provider store={store}>
          <ClipboardHistoryList showFavoritesOnly={true} />
        </Provider>
      );

      expect(screen.getByText('No Favorites Yet')).toBeInTheDocument();
      expect(screen.getByText('Star clipboard entries to add them to your favorites for quick access.')).toBeInTheDocument();
    });
  });

  describe('Tag Filter Interaction', () => {
    it('should show entries with selected tags when filtering is active', () => {
      const store = createMockStore({
        clipboard: {
          history: mockEntries,
          selectedTags: ['work'],
        },
      });

      render(
        <Provider store={store}>
          <ClipboardHistoryList showFavoritesOnly={false} />
        </Provider>
      );

      // Should show entries with 'work' tag
      expect(screen.getByText('First entry content')).toBeInTheDocument();
      expect(screen.getByText('Third entry content')).toBeInTheDocument();

      // Should not show entries without 'work' tag
      expect(screen.queryByText('Second entry content')).not.toBeInTheDocument();
      expect(screen.queryByText('Fourth entry content')).not.toBeInTheDocument();
    });

    it('should clear tag filter when all tags are deselected', () => {
      const store = createMockStore({
        clipboard: {
          history: mockEntries,
          selectedTags: [],
        },
      });

      render(
        <Provider store={store}>
          <ClipboardHistoryList showFavoritesOnly={false} />
        </Provider>
      );

      // All entries should be visible when no tags are selected
      expect(screen.getByText('First entry content')).toBeInTheDocument();
      expect(screen.getByText('Second entry content')).toBeInTheDocument();
      expect(screen.getByText('Third entry content')).toBeInTheDocument();
      expect(screen.getByText('Fourth entry content')).toBeInTheDocument();
    });
  });

  // Note: Search functionality is now handled in AppLayout, not in ClipboardHistoryList
  // These tests have been moved to AppLayout tests

  describe('Performance', () => {
    it('should handle large number of entries efficiently', () => {
      const largeEntryList = Array.from({ length: 1000 }, (_, i) => ({
        id: `entry-${i}`,
        content: `Entry ${i} content`,
        type: 'text' as const,
        timestamp: Date.now() - i * 1000,
        isFavorite: i % 10 === 0,
        name: i % 5 === 0 ? `Entry ${i}` : undefined,
        tags: i % 3 === 0 ? ['tag1'] : i % 3 === 1 ? ['tag2'] : ['tag3'],
      }));

      const store = createMockStore({
        clipboard: {
          history: largeEntryList,
        },
      });

      const startTime = performance.now();
      render(
        <Provider store={store}>
          <ClipboardHistoryList showFavoritesOnly={false} />
        </Provider>
      );
      const endTime = performance.now();

      // Should render within reasonable time (adjust threshold as needed)
      expect(endTime - startTime).toBeLessThan(2000); // Increased threshold for CI environments
    });
  });
});
