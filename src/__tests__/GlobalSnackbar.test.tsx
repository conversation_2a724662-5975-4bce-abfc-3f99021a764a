import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import { Provider } from 'react-redux';
import { ThemeProvider } from '@mui/material';
import { configureStore } from '@reduxjs/toolkit';
import GlobalSnackbar from '@/components/GlobalSnackbar';
import uiSlice, { showSnackbar, hideSnackbar } from '@/store/slices/uiSlice';
import themeWithOverrides from '@/theme';

const createMockStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      ui: uiSlice,
    },
    preloadedState: {
      ui: {
        snackbar: {
          open: false,
          message: '',
          severity: 'info' as const,
        },
        ...initialState,
      },
    },
  });
};

const renderWithProviders = (component: React.ReactElement, store = createMockStore()) => {
  return render(
    <Provider store={store}>
      <ThemeProvider theme={themeWithOverrides}>
        {component}
      </ThemeProvider>
    </Provider>
  );
};

describe('GlobalSnackbar', () => {
  it('does not render when snackbar is closed', () => {
    const store = createMockStore();
    renderWithProviders(<GlobalSnackbar />, store);
    
    // Snackbar should not be visible when closed
    expect(screen.queryByRole('alert')).not.toBeInTheDocument();
  });

  it('renders snackbar when open with info severity', () => {
    const store = createMockStore({
      snackbar: {
        open: true,
        message: 'Info message',
        severity: 'info' as const,
      },
    });
    
    renderWithProviders(<GlobalSnackbar />, store);
    
    expect(screen.getByRole('alert')).toBeInTheDocument();
    expect(screen.getByText('Info message')).toBeInTheDocument();
  });

  it('renders snackbar with success severity', () => {
    const store = createMockStore({
      snackbar: {
        open: true,
        message: 'Success message',
        severity: 'success' as const,
      },
    });

    renderWithProviders(<GlobalSnackbar />, store);

    const alert = screen.getByRole('alert');
    expect(alert).toBeInTheDocument();
    expect(screen.getByText('Success message')).toBeInTheDocument();
    // Check that the alert has the success class (filled variant)
    expect(alert).toHaveClass('MuiAlert-filledSuccess');
  });

  it('renders snackbar with error severity', () => {
    const store = createMockStore({
      snackbar: {
        open: true,
        message: 'Error message',
        severity: 'error' as const,
      },
    });

    renderWithProviders(<GlobalSnackbar />, store);

    const alert = screen.getByRole('alert');
    expect(alert).toBeInTheDocument();
    expect(screen.getByText('Error message')).toBeInTheDocument();
    expect(alert).toHaveClass('MuiAlert-filledError');
  });

  it('renders snackbar with warning severity', () => {
    const store = createMockStore({
      snackbar: {
        open: true,
        message: 'Warning message',
        severity: 'warning' as const,
      },
    });

    renderWithProviders(<GlobalSnackbar />, store);

    const alert = screen.getByRole('alert');
    expect(alert).toBeInTheDocument();
    expect(screen.getByText('Warning message')).toBeInTheDocument();
    expect(alert).toHaveClass('MuiAlert-filledWarning');
  });

  it('closes snackbar when close button is clicked', async () => {
    const store = createMockStore({
      snackbar: {
        open: true,
        message: 'Test message',
        severity: 'info' as const,
      },
    });
    
    renderWithProviders(<GlobalSnackbar />, store);
    
    // Find and click the close button
    const closeButton = screen.getByRole('button', { name: /close/i });
    fireEvent.click(closeButton);
    
    // Check that hideSnackbar action was dispatched
    await waitFor(() => {
      const state = store.getState();
      expect(state.ui.snackbar.open).toBe(false);
    });
  });

  it('does not close on clickaway', () => {
    const store = createMockStore({
      snackbar: {
        open: true,
        message: 'Test message',
        severity: 'info' as const,
      },
    });
    
    renderWithProviders(<GlobalSnackbar />, store);
    
    // Simulate clickaway event
    const snackbar = screen.getByRole('presentation');
    fireEvent.click(snackbar);
    
    // Snackbar should still be open
    const state = store.getState();
    expect(state.ui.snackbar.open).toBe(true);
  });

  it('integrates with Redux store actions', async () => {
    const store = createMockStore();
    renderWithProviders(<GlobalSnackbar />, store);

    // Initially closed
    expect(screen.queryByRole('alert')).not.toBeInTheDocument();

    // Show snackbar
    await act(async () => {
      store.dispatch(showSnackbar({ message: 'Test message', severity: 'success' }));
    });

    expect(screen.getByRole('alert')).toBeInTheDocument();
    expect(screen.getByText('Test message')).toBeInTheDocument();

    // Hide snackbar
    await act(async () => {
      store.dispatch(hideSnackbar());
    });

    // Should be hidden after state update
    const state = store.getState();
    expect(state.ui.snackbar.open).toBe(false);
  });

  it('has correct positioning and auto-hide duration', () => {
    const store = createMockStore({
      snackbar: {
        open: true,
        message: 'Test message',
        severity: 'info' as const,
      },
    });
    
    renderWithProviders(<GlobalSnackbar />, store);
    
    // Check that the snackbar is positioned correctly
    const snackbar = screen.getByRole('presentation');
    expect(snackbar).toBeInTheDocument();
    
    // The Snackbar component should have autoHideDuration set to 3000ms
    // This is tested implicitly through the component props
  });
});
