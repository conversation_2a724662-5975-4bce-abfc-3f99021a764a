import { configureStore } from '@reduxjs/toolkit';
import uiSlice, { showSnackbar, hideSnackbar } from '@/store/slices/uiSlice';

const createMockStore = () => {
  return configureStore({
    reducer: {
      ui: uiSlice,
    },
  });
};

describe('uiSlice', () => {
  describe('initial state', () => {
    it('should have correct initial state', () => {
      const store = createMockStore();
      const state = store.getState().ui;
      
      expect(state).toEqual({
        snackbar: {
          open: false,
          message: '',
          severity: 'info',
        },
      });
    });
  });

  describe('showSnackbar', () => {
    it('should show snackbar with message and default severity', () => {
      const store = createMockStore();
      
      store.dispatch(showSnackbar({ message: 'Test message' }));
      
      const state = store.getState().ui;
      expect(state.snackbar.open).toBe(true);
      expect(state.snackbar.message).toBe('Test message');
      expect(state.snackbar.severity).toBe('info');
    });

    it('should show snackbar with message and custom severity', () => {
      const store = createMockStore();
      
      store.dispatch(showSnackbar({ message: 'Error message', severity: 'error' }));
      
      const state = store.getState().ui;
      expect(state.snackbar.open).toBe(true);
      expect(state.snackbar.message).toBe('Error message');
      expect(state.snackbar.severity).toBe('error');
    });

    it('should show snackbar with success severity', () => {
      const store = createMockStore();
      
      store.dispatch(showSnackbar({ message: 'Success message', severity: 'success' }));
      
      const state = store.getState().ui;
      expect(state.snackbar.open).toBe(true);
      expect(state.snackbar.message).toBe('Success message');
      expect(state.snackbar.severity).toBe('success');
    });

    it('should show snackbar with warning severity', () => {
      const store = createMockStore();
      
      store.dispatch(showSnackbar({ message: 'Warning message', severity: 'warning' }));
      
      const state = store.getState().ui;
      expect(state.snackbar.open).toBe(true);
      expect(state.snackbar.message).toBe('Warning message');
      expect(state.snackbar.severity).toBe('warning');
    });
  });

  describe('hideSnackbar', () => {
    it('should hide snackbar', () => {
      const store = createMockStore();
      
      // First show a snackbar
      store.dispatch(showSnackbar({ message: 'Test message' }));
      expect(store.getState().ui.snackbar.open).toBe(true);
      
      // Then hide it
      store.dispatch(hideSnackbar());
      
      const state = store.getState().ui;
      expect(state.snackbar.open).toBe(false);
      // Message and severity should remain unchanged
      expect(state.snackbar.message).toBe('Test message');
      expect(state.snackbar.severity).toBe('info');
    });

    it('should hide snackbar when already hidden', () => {
      const store = createMockStore();
      
      // Hide snackbar when it's already hidden
      store.dispatch(hideSnackbar());
      
      const state = store.getState().ui;
      expect(state.snackbar.open).toBe(false);
      expect(state.snackbar.message).toBe('');
      expect(state.snackbar.severity).toBe('info');
    });
  });

  describe('multiple snackbar operations', () => {
    it('should handle multiple show/hide operations correctly', () => {
      const store = createMockStore();
      
      // Show first snackbar
      store.dispatch(showSnackbar({ message: 'First message', severity: 'success' }));
      let state = store.getState().ui;
      expect(state.snackbar.open).toBe(true);
      expect(state.snackbar.message).toBe('First message');
      expect(state.snackbar.severity).toBe('success');
      
      // Show second snackbar (should replace first)
      store.dispatch(showSnackbar({ message: 'Second message', severity: 'error' }));
      state = store.getState().ui;
      expect(state.snackbar.open).toBe(true);
      expect(state.snackbar.message).toBe('Second message');
      expect(state.snackbar.severity).toBe('error');
      
      // Hide snackbar
      store.dispatch(hideSnackbar());
      state = store.getState().ui;
      expect(state.snackbar.open).toBe(false);
      expect(state.snackbar.message).toBe('Second message');
      expect(state.snackbar.severity).toBe('error');
    });
  });
});
