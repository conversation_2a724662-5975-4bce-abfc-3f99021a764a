import { configureStore } from '@reduxjs/toolkit';
import clipboardSlice, {
  setSearchQuery,
  setSelectedEntry,
  addEntry,
  updateMaxHistoryLength,
  getClipboardHistory,
  saveClipboardEntry,
  toggleFavorite,
  clearHistory,
  deleteEntry,
  startMonitoring,
  stopMonitoring,
  checkMonitoringStatus,
} from '@/store/slices/clipboardSlice';
import type { ClipboardEntry } from '@/types/clipboard';

const mockEntries: ClipboardEntry[] = [
  {
    id: '1',
    content: 'First entry',
    type: 'text',
    timestamp: Date.now() - 1000,
    isFavorite: false,
    isPinned: false,
    tags: [],
  },
  {
    id: '2',
    content: 'Second entry',
    type: 'text',
    timestamp: Date.now() - 2000,
    isFavorite: true,
    isPinned: false,
    tags: [],
  },
  {
    id: '3',
    content: 'Third entry',
    type: 'text',
    timestamp: Date.now() - 3000,
    isFavorite: false,
    isPinned: false,
    tags: [],
  },
];

const createStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      clipboard: clipboardSlice,
    },
    preloadedState: {
      clipboard: {
        history: [],
        favorites: [],
        maxHistoryLength: 100,
        isMonitoring: false,
        searchQuery: '',
        selectedEntryId: null,
        selectedTags: [],
        ...initialState,
      },
    },
  });
};

describe('clipboardSlice', () => {
  beforeEach(() => {
    globalThis.mockTauriInvoke.mockClear();
  });

  describe('reducers', () => {
    it('should handle setSearchQuery', () => {
      const store = createStore();
      
      store.dispatch(setSearchQuery('test query'));
      
      expect(store.getState().clipboard.searchQuery).toBe('test query');
    });

    it('should handle setSelectedEntry', () => {
      const store = createStore();
      
      store.dispatch(setSelectedEntry('entry-1'));
      
      expect(store.getState().clipboard.selectedEntryId).toBe('entry-1');
    });

    it('should handle addEntry', () => {
      const store = createStore();
      const newEntry: ClipboardEntry = {
        id: 'new-1',
        content: 'New entry',
        type: 'text',
        timestamp: Date.now(),
        isFavorite: false,
    isPinned: false,
        tags: [],
      };
      
      store.dispatch(addEntry(newEntry));
      
      const state = store.getState().clipboard;
      expect(state.history).toHaveLength(1);
      expect(state.history[0]).toEqual(newEntry);
    });

    it('should add entry to beginning of history', () => {
      const store = createStore({ history: mockEntries });
      const newEntry: ClipboardEntry = {
        id: 'new-1',
        content: 'New entry',
        type: 'text',
        timestamp: Date.now(),
        isFavorite: false,
    isPinned: false,
        tags: [],
      };
      
      store.dispatch(addEntry(newEntry));
      
      const state = store.getState().clipboard;
      expect(state.history[0]).toEqual(newEntry);
      expect(state.history).toHaveLength(4);
    });

    it('should trim history when exceeding max length but preserve favorites', () => {
      const store = createStore({
        history: mockEntries,
        maxHistoryLength: 2, // Smaller than mockEntries length
      });
      const newEntry: ClipboardEntry = {
        id: 'new-1',
        content: 'New entry',
        type: 'text',
        timestamp: Date.now(),
        isFavorite: false,
    isPinned: false,
        tags: [],
      };

      store.dispatch(addEntry(newEntry));

      const state = store.getState().clipboard;
      // Should have 3 items: 2 non-favorites + 1 favorite (mockEntries[1])
      expect(state.history).toHaveLength(3);
      expect(state.history[0]).toEqual(newEntry);
      // Favorite should still be present
      expect(state.history.find(entry => entry.id === '2')).toBeDefined();
      expect(state.history.find(entry => entry.id === '2')?.isFavorite).toBe(true);
    });

    it('should handle updateMaxHistoryLength and preserve favorites', () => {
      const store = createStore({
        history: mockEntries,
        maxHistoryLength: 100,
      });

      store.dispatch(updateMaxHistoryLength(1)); // Very restrictive limit

      const state = store.getState().clipboard;
      expect(state.maxHistoryLength).toBe(1);
      // Should have 2 items: 1 non-favorite + 1 favorite (mockEntries[1])
      expect(state.history).toHaveLength(2);
      // Favorite should still be present
      expect(state.history.find(entry => entry.id === '2')).toBeDefined();
      expect(state.history.find(entry => entry.id === '2')?.isFavorite).toBe(true);
    });
  });

  describe('async thunks', () => {
    describe('getClipboardHistory', () => {
      it('should fetch clipboard history successfully', async () => {
        globalThis.mockTauriInvoke.mockResolvedValueOnce(mockEntries);
        const store = createStore();
        
        await store.dispatch(getClipboardHistory());
        
        const state = store.getState().clipboard;
        expect(state.history).toEqual(mockEntries);
        expect(state.favorites).toEqual([mockEntries[1]]); // Only favorite entry
        expect(globalThis.mockTauriInvoke).toHaveBeenCalledWith('get_clipboard_history');
      });

      it('should handle fetch error', async () => {
        globalThis.mockTauriInvoke.mockRejectedValueOnce(new Error('Fetch failed'));
        const store = createStore();
        
        const result = await store.dispatch(getClipboardHistory());
        
        expect(result.type).toBe('clipboard/getHistory/rejected');
      });
    });

    describe('saveClipboardEntry', () => {
      it('should save clipboard entry successfully', async () => {
        const newEntry: ClipboardEntry = {
          id: 'saved-1',
          content: 'Saved entry',
          type: 'text',
          timestamp: Date.now(),
          isFavorite: false,
    isPinned: false,
          tags: [],
        };
        globalThis.mockTauriInvoke.mockResolvedValueOnce(newEntry);
        const store = createStore();
        
        await store.dispatch(saveClipboardEntry({
          content: 'Saved entry',
          type: 'text',
          isFavorite: false,
          isPinned: false,
          tags: [],
        }));
        
        const state = store.getState().clipboard;
        expect(state.history[0]).toEqual(newEntry);
        expect(globalThis.mockTauriInvoke).toHaveBeenCalledWith('save_clipboard_entry', {
          entry: {
            content: 'Saved entry',
            type: 'text',
            isFavorite: false,
            isPinned: false,
            tags: [],
          },
        });
      });

      it('should trim history after saving when exceeding max length but preserve favorites', async () => {
        const newEntry: ClipboardEntry = {
          id: 'saved-1',
          content: 'Saved entry',
          type: 'text',
          timestamp: Date.now(),
          isFavorite: false,
    isPinned: false,
          tags: [],
        };
        globalThis.mockTauriInvoke.mockResolvedValueOnce(newEntry);
        const store = createStore({
          history: mockEntries,
          maxHistoryLength: 2, // Smaller than mockEntries length
        });

        await store.dispatch(saveClipboardEntry({
          content: 'Saved entry',
          type: 'text',
          isFavorite: false,
          isPinned: false,
          tags: [],
        }));

        const state = store.getState().clipboard;
        // Should have 3 items: 2 non-favorites + 1 favorite (mockEntries[1])
        expect(state.history).toHaveLength(3);
        expect(state.history[0]).toEqual(newEntry);
        // Favorite should still be present
        expect(state.history.find(entry => entry.id === '2')).toBeDefined();
        expect(state.history.find(entry => entry.id === '2')?.isFavorite).toBe(true);
      });
    });

    describe('toggleFavorite', () => {
      it('should toggle favorite status successfully', async () => {
        const updatedEntry: ClipboardEntry = {
          ...mockEntries[0],
          isFavorite: true,
        };
        globalThis.mockTauriInvoke.mockResolvedValueOnce(updatedEntry);
        const store = createStore({ history: mockEntries });
        
        await store.dispatch(toggleFavorite('1'));
        
        const state = store.getState().clipboard;
        expect(state.history[0].isFavorite).toBe(true);
        expect(state.favorites).toContainEqual(updatedEntry);
        expect(globalThis.mockTauriInvoke).toHaveBeenCalledWith('toggle_favorite', { entryId: '1' });
      });

      it('should remove from favorites when toggling off', async () => {
        const updatedEntry: ClipboardEntry = {
          ...mockEntries[1],
          isFavorite: false,
        };
        globalThis.mockTauriInvoke.mockResolvedValueOnce(updatedEntry);
        const store = createStore({ 
          history: mockEntries,
          favorites: [mockEntries[1]],
        });
        
        await store.dispatch(toggleFavorite('2'));
        
        const state = store.getState().clipboard;
        expect(state.history[1].isFavorite).toBe(false);
        expect(state.favorites).not.toContainEqual(updatedEntry);
      });

      it('should update existing favorite entry', async () => {
        const updatedEntry: ClipboardEntry = {
          ...mockEntries[1],
          content: 'Updated content',
          isFavorite: true,
        };
        globalThis.mockTauriInvoke.mockResolvedValueOnce(updatedEntry);
        const store = createStore({ 
          history: mockEntries,
          favorites: [mockEntries[1]],
        });
        
        await store.dispatch(toggleFavorite('2'));
        
        const state = store.getState().clipboard;
        expect(state.favorites[0].content).toBe('Updated content');
      });
    });

    describe('clearHistory', () => {
      it('should clear history but preserve favorites', async () => {
        globalThis.mockTauriInvoke.mockResolvedValueOnce(undefined);
        const store = createStore({
          history: mockEntries,
          favorites: [mockEntries[1]], // mockEntries[1] is favorite
          selectedEntryId: '1',
        });

        await store.dispatch(clearHistory());

        const state = store.getState().clipboard;
        // Should only have the favorite entry remaining
        expect(state.history).toHaveLength(1);
        expect(state.history[0]).toEqual(mockEntries[1]);
        expect(state.history[0].isFavorite).toBe(true);
        expect(state.selectedEntryId).toBeNull();
        expect(globalThis.mockTauriInvoke).toHaveBeenCalledWith('clear_clipboard_history');
      });

      it('should clear all history when no favorites exist', async () => {
        globalThis.mockTauriInvoke.mockResolvedValueOnce(undefined);
        const nonFavoriteEntries = mockEntries.map(entry => ({ ...entry, isFavorite: false }));
        const store = createStore({
          history: nonFavoriteEntries,
          favorites: [],
          selectedEntryId: '1',
        });

        await store.dispatch(clearHistory());

        const state = store.getState().clipboard;
        expect(state.history).toEqual([]);
        expect(state.selectedEntryId).toBeNull();
        expect(globalThis.mockTauriInvoke).toHaveBeenCalledWith('clear_clipboard_history');
      });
    });

    describe('deleteEntry', () => {
      it('should delete entry successfully', async () => {
        globalThis.mockTauriInvoke.mockResolvedValueOnce(undefined);
        const store = createStore({ 
          history: mockEntries,
          favorites: [mockEntries[1]],
          selectedEntryId: '2',
        });
        
        await store.dispatch(deleteEntry('2'));
        
        const state = store.getState().clipboard;
        expect(state.history).not.toContainEqual(mockEntries[1]);
        expect(state.favorites).toEqual([]);
        expect(state.selectedEntryId).toBeNull();
        expect(globalThis.mockTauriInvoke).toHaveBeenCalledWith('delete_clipboard_entry', { entryId: '2' });
      });

      it('should not change selectedEntryId if different entry is deleted', async () => {
        globalThis.mockTauriInvoke.mockResolvedValueOnce(undefined);
        const store = createStore({ 
          history: mockEntries,
          selectedEntryId: '1',
        });
        
        await store.dispatch(deleteEntry('2'));
        
        const state = store.getState().clipboard;
        expect(state.selectedEntryId).toBe('1');
      });
    });

    describe('startMonitoring', () => {
      it('should start monitoring successfully', async () => {
        globalThis.mockTauriInvoke.mockResolvedValueOnce(undefined);
        const store = createStore();
        
        await store.dispatch(startMonitoring());
        
        const state = store.getState().clipboard;
        expect(state.isMonitoring).toBe(true);
        expect(globalThis.mockTauriInvoke).toHaveBeenCalledWith('start_clipboard_monitoring');
      });
    });

    describe('stopMonitoring', () => {
      it('should stop monitoring successfully', async () => {
        globalThis.mockTauriInvoke.mockResolvedValueOnce(undefined);
        const store = createStore({ isMonitoring: true });
        
        await store.dispatch(stopMonitoring());
        
        const state = store.getState().clipboard;
        expect(state.isMonitoring).toBe(false);
        expect(globalThis.mockTauriInvoke).toHaveBeenCalledWith('stop_clipboard_monitoring');
      });
    });

    describe('checkMonitoringStatus', () => {
      it('should check monitoring status successfully', async () => {
        globalThis.mockTauriInvoke.mockResolvedValueOnce(true);
        const store = createStore();
        
        await store.dispatch(checkMonitoringStatus());
        
        const state = store.getState().clipboard;
        expect(state.isMonitoring).toBe(true);
        expect(globalThis.mockTauriInvoke).toHaveBeenCalledWith('is_monitoring_clipboard');
      });
    });
  });

  describe('initial state', () => {
    it('should have correct initial state', () => {
      const store = createStore();
      const state = store.getState().clipboard;
      
      expect(state).toEqual({
        history: [],
        favorites: [],
        maxHistoryLength: 100,
        isMonitoring: false,
        searchQuery: '',
        selectedEntryId: null,
        selectedTags: [],
      });
    });
  });

  describe('edge cases', () => {
    it('should handle empty history when trimming', () => {
      const store = createStore({ history: [] });
      
      store.dispatch(updateMaxHistoryLength(50));
      
      const state = store.getState().clipboard;
      expect(state.history).toEqual([]);
      expect(state.maxHistoryLength).toBe(50);
    });

    it('should handle toggle favorite for non-existent entry', async () => {
      const updatedEntry: ClipboardEntry = {
        id: 'non-existent',
        content: 'New entry',
        type: 'text',
        timestamp: Date.now(),
        isFavorite: true,
    isPinned: false,
        tags: [],
      };
      globalThis.mockTauriInvoke.mockResolvedValueOnce(updatedEntry);
      const store = createStore({ history: mockEntries });
      
      await store.dispatch(toggleFavorite('non-existent'));
      
      const state = store.getState().clipboard;
      // Should add to favorites even if not in history
      expect(state.favorites).toContainEqual(updatedEntry);
    });

    it('should handle adding entry with zero max history length but preserve favorites', () => {
      const favoriteEntry: ClipboardEntry = {
        id: 'fav-1',
        content: 'Favorite entry',
        type: 'text',
        timestamp: Date.now() - 1000,
        isFavorite: true,
    isPinned: false,
        tags: [],
      };
      const store = createStore({
        maxHistoryLength: 0,
        history: [favoriteEntry],
        favorites: [favoriteEntry],
      });
      const newEntry: ClipboardEntry = {
        id: 'new-1',
        content: 'New entry',
        type: 'text',
        timestamp: Date.now(),
        isFavorite: false,
    isPinned: false,
        tags: [],
      };

      store.dispatch(addEntry(newEntry));

      const state = store.getState().clipboard;
      // Should only have the favorite entry, new non-favorite should be rejected
      expect(state.history).toHaveLength(1);
      expect(state.history[0]).toEqual(favoriteEntry);
    });
  });

  describe('Bug Fix Tests - Favorited Items Behavior', () => {
    describe('Primary Bug: Favorited items should not be removed from history', () => {
      it('should preserve favorites when adding new entries exceeds max length', () => {
        const favoriteEntry: ClipboardEntry = {
          id: 'fav-1',
          content: 'Important favorite',
          type: 'text',
          timestamp: Date.now() - 5000,
          isFavorite: true,
    isPinned: false,
          tags: [],
        };

        const regularEntries: ClipboardEntry[] = [
          {
            id: 'reg-1',
            content: 'Regular 1',
            type: 'text',
            timestamp: Date.now() - 1000,
            isFavorite: false,
    isPinned: false,
            tags: [],
          },
          {
            id: 'reg-2',
            content: 'Regular 2',
            type: 'text',
            timestamp: Date.now() - 2000,
            isFavorite: false,
    isPinned: false,
            tags: [],
          },
        ];

        const store = createStore({
          history: [favoriteEntry, ...regularEntries],
          favorites: [favoriteEntry],
          maxHistoryLength: 2, // Only allow 2 regular entries
        });

        // Add multiple new entries
        const newEntry1: ClipboardEntry = {
          id: 'new-1',
          content: 'New entry 1',
          type: 'text',
          timestamp: Date.now(),
          isFavorite: false,
    isPinned: false,
          tags: [],
        };

        const newEntry2: ClipboardEntry = {
          id: 'new-2',
          content: 'New entry 2',
          type: 'text',
          timestamp: Date.now() + 1000,
          isFavorite: false,
    isPinned: false,
          tags: [],
        };

        store.dispatch(addEntry(newEntry1));
        store.dispatch(addEntry(newEntry2));

        const state = store.getState().clipboard;

        // Should have 3 items: 2 most recent non-favorites + 1 favorite
        expect(state.history).toHaveLength(3);

        // Favorite should still be present
        expect(state.history.find(entry => entry.id === 'fav-1')).toBeDefined();
        expect(state.history.find(entry => entry.id === 'fav-1')?.isFavorite).toBe(true);

        // Most recent non-favorites should be present
        expect(state.history.find(entry => entry.id === 'new-2')).toBeDefined();
        expect(state.history.find(entry => entry.id === 'new-1')).toBeDefined();

        // Oldest regular entry should be removed
        expect(state.history.find(entry => entry.id === 'reg-2')).toBeUndefined();
      });

      it('should preserve favorites when updating max history length', () => {
        const favorites: ClipboardEntry[] = [
          {
            id: 'fav-1',
            content: 'Favorite 1',
            type: 'text',
            timestamp: Date.now() - 1000,
            isFavorite: true,
    isPinned: false,
            tags: [],
          },
          {
            id: 'fav-2',
            content: 'Favorite 2',
            type: 'text',
            timestamp: Date.now() - 2000,
            isFavorite: true,
    isPinned: false,
            tags: [],
          },
        ];

        const regularEntries: ClipboardEntry[] = Array.from({ length: 5 }, (_, i) => ({
          id: `reg-${i}`,
          content: `Regular ${i}`,
          type: 'text',
          timestamp: Date.now() - (3000 + i * 1000),
          isFavorite: false,
          isPinned: false,
          tags: [],
        }));

        const store = createStore({
          history: [...favorites, ...regularEntries],
          favorites,
          maxHistoryLength: 10,
        });

        // Reduce max history length significantly
        store.dispatch(updateMaxHistoryLength(2));

        const state = store.getState().clipboard;

        // Should have 4 items: 2 favorites + 2 most recent regular entries
        expect(state.history).toHaveLength(4);

        // Both favorites should still be present
        expect(state.history.filter(entry => entry.isFavorite)).toHaveLength(2);
        expect(state.history.find(entry => entry.id === 'fav-1')).toBeDefined();
        expect(state.history.find(entry => entry.id === 'fav-2')).toBeDefined();

        // Only 2 most recent regular entries should remain
        expect(state.history.filter(entry => !entry.isFavorite)).toHaveLength(2);
        expect(state.history.find(entry => entry.id === 'reg-0')).toBeDefined();
        expect(state.history.find(entry => entry.id === 'reg-1')).toBeDefined();
      });

      it('should preserve favorites when clearing history', async () => {
        const favoriteEntry: ClipboardEntry = {
          id: 'fav-1',
          content: 'Important favorite',
          type: 'text',
          timestamp: Date.now() - 1000,
          isFavorite: true,
    isPinned: false,
          tags: [],
        };

        const regularEntries: ClipboardEntry[] = [
          {
            id: 'reg-1',
            content: 'Regular 1',
            type: 'text',
            timestamp: Date.now() - 2000,
            isFavorite: false,
    isPinned: false,
            tags: [],
          },
          {
            id: 'reg-2',
            content: 'Regular 2',
            type: 'text',
            timestamp: Date.now() - 3000,
            isFavorite: false,
    isPinned: false,
            tags: [],
          },
        ];

        globalThis.mockTauriInvoke.mockResolvedValueOnce(undefined);
        const store = createStore({
          history: [favoriteEntry, ...regularEntries],
          favorites: [favoriteEntry],
        });

        await store.dispatch(clearHistory());

        const state = store.getState().clipboard;

        // Should only have the favorite entry
        expect(state.history).toHaveLength(1);
        expect(state.history[0]).toEqual(favoriteEntry);
        expect(state.history[0].isFavorite).toBe(true);

        // Regular entries should be gone
        expect(state.history.find(entry => entry.id === 'reg-1')).toBeUndefined();
        expect(state.history.find(entry => entry.id === 'reg-2')).toBeUndefined();
      });
    });

    describe('Secondary Bug: Favorites should not count toward max history length', () => {
      it('should allow unlimited favorites beyond max history length', () => {
        const favorites: ClipboardEntry[] = Array.from({ length: 10 }, (_, i) => ({
          id: `fav-${i}`,
          content: `Favorite ${i}`,
          type: 'text',
          timestamp: Date.now() - (i * 1000),
          isFavorite: true,
          isPinned: false,
          tags: [],
        }));

        const store = createStore({
          history: favorites,
          favorites,
          maxHistoryLength: 3, // Much smaller than number of favorites
        });

        // Add regular entries up to the limit
        const regularEntries: ClipboardEntry[] = Array.from({ length: 5 }, (_, i) => ({
          id: `reg-${i}`,
          content: `Regular ${i}`,
          type: 'text',
          timestamp: Date.now() + (i * 1000),
          isFavorite: false,
          isPinned: false,
          tags: [],
        }));

        regularEntries.forEach(entry => {
          store.dispatch(addEntry(entry));
        });

        const state = store.getState().clipboard;

        // Should have all 10 favorites + 3 most recent regular entries
        expect(state.history).toHaveLength(13);

        // All favorites should still be present
        expect(state.history.filter(entry => entry.isFavorite)).toHaveLength(10);

        // Only 3 most recent regular entries should be present
        const regularInHistory = state.history.filter(entry => !entry.isFavorite);
        expect(regularInHistory).toHaveLength(3);
        expect(regularInHistory.find(entry => entry.id === 'reg-4')).toBeDefined();
        expect(regularInHistory.find(entry => entry.id === 'reg-3')).toBeDefined();
        expect(regularInHistory.find(entry => entry.id === 'reg-2')).toBeDefined();

        // Older regular entries should be removed
        expect(state.history.find(entry => entry.id === 'reg-0')).toBeUndefined();
        expect(state.history.find(entry => entry.id === 'reg-1')).toBeUndefined();
      });

      it('should maintain correct count when max length is very small', () => {
        // Start with favorites already in history
        const favorites: ClipboardEntry[] = [
          {
            id: 'fav-1',
            content: 'Favorite 1',
            type: 'text',
            timestamp: Date.now() - 1000,
            isFavorite: true,
    isPinned: false,
            tags: [],
          },
          {
            id: 'fav-2',
            content: 'Favorite 2',
            type: 'text',
            timestamp: Date.now() - 2000,
            isFavorite: true,
    isPinned: false,
            tags: [],
          },
        ];

        const store = createStore({
          maxHistoryLength: 1, // Very restrictive
          history: favorites,
          favorites: favorites,
        });

        // Add regular entries
        const regularEntry: ClipboardEntry = {
          id: 'reg-1',
          content: 'Regular 1',
          type: 'text',
          timestamp: Date.now(),
          isFavorite: false,
    isPinned: false,
          tags: [],
        };

        store.dispatch(addEntry(regularEntry));

        const state = store.getState().clipboard;

        // Should have both favorites + 1 regular entry (despite max length = 1)
        expect(state.history.filter(entry => entry.isFavorite)).toHaveLength(2);
        expect(state.history.filter(entry => !entry.isFavorite)).toHaveLength(1);
        expect(state.history).toHaveLength(3); // Total should be 3
      });
    });
  });
});
