import { render, screen, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import App from '@/App';
import clipboardSlice from '@/store/slices/clipboardSlice';
import preferencesSlice from '@/store/slices/preferencesSlice';

// Mock the hooks
vi.mock('@/hooks/useClipboardMonitoring', () => ({
  useClipboardMonitoring: vi.fn(() => ({ isMonitoring: false })),
}));

vi.mock('@/hooks/useKeyboardShortcuts', () => ({
  useKeyboardShortcuts: vi.fn(() => ({ selectedIndex: -1, selectedEntryId: null })),
}));

const createMockStore = () => {
  return configureStore({
    reducer: {
      clipboard: clipboardSlice,
      preferences: preferencesSlice,
    },
    preloadedState: {
      clipboard: {
        history: [],
        favorites: [],
        maxHistoryLength: 100,
        isMonitoring: false,
        searchQuery: '',
        selectedEntryId: null,
        selectedTags: [],
      },
      preferences: {
        theme: 'dark' as const,
        shortcuts: {
          toggleApp: 'CommandOrControl+Shift+V',
          clearHistory: 'CommandOrControl+Shift+Delete',
          toggleFavorite: 'CommandOrControl+D',
          copySelected: 'Enter',
          deleteSelected: 'Delete',
          search: 'CommandOrControl+F',
          navigateUp: 'ArrowUp',
          navigateDown: 'ArrowDown',
        },
        autoStart: false,
        showInSystemTray: true,
        maxHistoryLength: 100,
        enableNotifications: true,
      },
    },
  });
};

describe('App', () => {
  beforeEach(() => {
    globalThis.mockTauriInvoke.mockClear();
  });

  it('renders without crashing', () => {
    const store = createMockStore();
    render(
      <Provider store={store}>
        <App />
      </Provider>
    );

    // Check that the app renders with expected elements instead of "Paste King"
    expect(screen.getByText('History (0)')).toBeInTheDocument();
    expect(screen.getByText('Favorites (0)')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Search clipboard... (use #tag for tag filtering)')).toBeInTheDocument();
  });

  it('initializes preferences on mount', async () => {
    globalThis.mockTauriInvoke.mockResolvedValueOnce({
      theme: 'light',
      autoStart: true,
    });

    const store = createMockStore();
    render(
      <Provider store={store}>
        <App />
      </Provider>
    );

    await waitFor(() => {
      expect(globalThis.mockTauriInvoke).toHaveBeenCalledWith('get_preferences');
    });
  });

  it('handles initialization errors gracefully', async () => {
    const testError = new Error('Failed to load preferences');
    globalThis.mockTauriInvoke.mockRejectedValueOnce(testError);
    
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

    const store = createMockStore();
    render(
      <Provider store={store}>
        <App />
      </Provider>
    );

    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith('Failed to initialize app:', expect.objectContaining({
        message: 'Failed to load preferences'
      }));
    });

    consoleSpy.mockRestore();
  });

  it('applies theme provider correctly', () => {
    const store = createMockStore();
    const { container } = render(
      <Provider store={store}>
        <App />
      </Provider>
    );

    // Check that MUI theme is applied by looking for theme-related elements
    // CssBaseline applies global styles but doesn't necessarily add a specific class
    expect(container.querySelector('[class*="Mui"]')).toBeInTheDocument();
  });
});
