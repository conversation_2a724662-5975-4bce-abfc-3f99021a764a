import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import { Provider } from 'react-redux';
import { ThemeProvider } from '@mui/material';
import { configureStore } from '@reduxjs/toolkit';
import AppLayout from '@/components/AppLayout';
import clipboardSlice from '@/store/slices/clipboardSlice';
import preferencesSlice from '@/store/slices/preferencesSlice';
import uiSlice from '@/store/slices/uiSlice';
import themeWithOverrides from '@/theme';
import type { ClipboardEntry } from '@/types/clipboard';

// Mock the hooks
vi.mock('@/hooks/useKeyboardShortcuts', () => ({
  useKeyboardShortcuts: vi.fn(() => ({ selectedIndex: -1, selectedEntryId: null })),
}));

const mockEntries: ClipboardEntry[] = [
  {
    id: '1',
    content: 'Test content 1',
    type: 'text',
    timestamp: Date.now() - 1000,
    isFavorite: false,
    isPinned: false,
    tags: [],
  },
  {
    id: '2',
    content: 'Test content 2',
    type: 'text',
    timestamp: Date.now() - 2000,
    isFavorite: true,
    isPinned: false,
    tags: [],
  },
];

const createMockStore = (overrides = {}) => {
  return configureStore({
    reducer: {
      clipboard: clipboardSlice,
      preferences: preferencesSlice,
      ui: uiSlice,
    },
    preloadedState: {
      clipboard: {
        history: mockEntries,
        favorites: mockEntries.filter(e => e.isFavorite),
        maxHistoryLength: 100,
        isMonitoring: true,
        searchQuery: '',
        selectedEntryId: null,
        selectedTags: [],
        ...overrides,
      },
      preferences: {
        theme: 'dark' as const,
        shortcuts: {
          toggleApp: 'CommandOrControl+Shift+V',
          clearHistory: 'CommandOrControl+Shift+Delete',
          toggleFavorite: 'CommandOrControl+D',
          copySelected: 'Enter',
          deleteSelected: 'Delete',
          search: 'CommandOrControl+F',
          navigateUp: 'ArrowUp',
          navigateDown: 'ArrowDown',
        },
        autoStart: false,
        showInSystemTray: true,
        maxHistoryLength: 100,
        enableNotifications: true,
      },
      ui: {
        snackbar: {
          open: false,
          message: '',
          severity: 'info',
        },
      },
    },
  });
};

const renderWithProviders = (component: React.ReactElement, store = createMockStore()) => {
  return render(
    <Provider store={store}>
      <ThemeProvider theme={themeWithOverrides}>
        {component}
      </ThemeProvider>
    </Provider>
  );
};

describe('AppLayout', () => {
  beforeEach(() => {
    globalThis.mockTauriInvoke.mockClear();
    // Mock window.confirm
    globalThis.confirm = vi.fn(() => true);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('renders the main layout components', async () => {
    await act(async () => {
      renderWithProviders(<AppLayout />);
    });

    // Check for the tab navigation
    expect(screen.getByText('History (2)')).toBeInTheDocument();
    expect(screen.getByText('Favorites (1)')).toBeInTheDocument();

    // Check for search field
    expect(screen.getByPlaceholderText('Search clipboard... (use #tag for tag filtering)')).toBeInTheDocument();

    // Check for settings button (by tooltip)
    expect(screen.getByRole('button', { name: /settings/i })).toBeInTheDocument();
  });

  it('displays correct badge counts in tabs', async () => {
    await act(async () => {
      renderWithProviders(<AppLayout />);
    });

    // Check that tabs show correct counts
    expect(screen.getByText('History (2)')).toBeInTheDocument();
    expect(screen.getByText('Favorites (1)')).toBeInTheDocument();
  });

  it('switches between views correctly', async () => {
    await act(async () => {
      renderWithProviders(<AppLayout />);
    });

    // Initially shows history view
    expect(screen.getByText('History (2)')).toBeInTheDocument();

    // Click on favorites tab
    const favoritesTab = screen.getByText('Favorites (1)');
    await act(async () => {
      fireEvent.click(favoritesTab);
    });

    // Should still show the favorites tab as selected
    expect(screen.getByText('Favorites (1)')).toBeInTheDocument();

    // Click on settings button
    const settingsButton = screen.getByRole('button', { name: /settings/i });
    await act(async () => {
      fireEvent.click(settingsButton);
    });

    // Should show settings content
    expect(screen.getByText('Settings')).toBeInTheDocument();
  });

  it('shows monitoring button correctly when monitoring', async () => {
    await act(async () => {
      renderWithProviders(<AppLayout />);
    });

    // Should show stop button when monitoring
    expect(screen.getByRole('button', { name: /stop clipboard monitoring/i })).toBeInTheDocument();
  });

  it('shows start button when not monitoring', async () => {
    const store = createMockStore({ isMonitoring: false });
    await act(async () => {
      renderWithProviders(<AppLayout />, store);
    });

    // Should show start button when not monitoring
    expect(screen.getByRole('button', { name: /start clipboard monitoring/i })).toBeInTheDocument();
  });

  it('handles start/stop monitoring', async () => {
    globalThis.mockTauriInvoke.mockResolvedValue(undefined);

    await act(async () => {
      renderWithProviders(<AppLayout />);
    });

    // Click stop monitoring button (since it's currently monitoring)
    const stopButton = screen.getByRole('button', { name: /stop clipboard monitoring/i });
    await act(async () => {
      fireEvent.click(stopButton);
    });

    await waitFor(() => {
      expect(globalThis.mockTauriInvoke).toHaveBeenCalledWith('stop_clipboard_monitoring');
    });
  });

  it('handles search input changes', async () => {
    await act(async () => {
      renderWithProviders(<AppLayout />);
    });

    const searchInput = screen.getByPlaceholderText('Search clipboard... (use #tag for tag filtering)');

    await act(async () => {
      fireEvent.change(searchInput, { target: { value: 'test search' } });
    });

    expect(searchInput).toHaveValue('test search');
  });

  it('handles start monitoring when currently stopped', async () => {
    globalThis.mockTauriInvoke.mockResolvedValue(undefined);
    const store = createMockStore({ isMonitoring: false });

    await act(async () => {
      renderWithProviders(<AppLayout />, store);
    });

    // Click start monitoring button
    const startButton = screen.getByRole('button', { name: /start clipboard monitoring/i });
    await act(async () => {
      fireEvent.click(startButton);
    });

    await waitFor(() => {
      expect(globalThis.mockTauriInvoke).toHaveBeenCalledWith('start_clipboard_monitoring');
    });
  });
});
