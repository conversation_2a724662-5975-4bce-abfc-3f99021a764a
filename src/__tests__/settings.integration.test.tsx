import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import Settings from '@/components/Settings';
import clipboardSlice from '@/store/slices/clipboardSlice';
import preferencesSlice from '@/store/slices/preferencesSlice';
import type { PreferencesState } from '@/types/clipboard';

// Mock Tauri API
vi.mock('@tauri-apps/api/core', () => ({
  invoke: vi.fn(),
}));

const mockInvoke = vi.mocked(await import('@tauri-apps/api/core')).invoke;

// Mock ShortcutDialog component
vi.mock('@/components/ShortcutDialog', () => ({
  default: ({ open, onClose, onSave, title, currentShortcut }: any) => {
    if (!open) return null;
    return (
      <div data-testid="shortcut-dialog">
        <h3>{title}</h3>
        <p>Current: {currentShortcut}</p>
        <button onClick={() => onSave('F1')}>Save Shortcut</button>
        <button onClick={onClose}>Cancel</button>
      </div>
    );
  },
}));

describe('Settings Integration Tests', () => {
  const createStore = (initialPreferences?: Partial<PreferencesState>) => {
    return configureStore({
      reducer: {
        clipboard: clipboardSlice,
        preferences: preferencesSlice,
      },
      preloadedState: {
        preferences: {
          theme: 'dark' as const,
          shortcuts: {
            toggleApp: 'CommandOrControl+Shift+V',
            clearHistory: 'CommandOrControl+Shift+Delete',
            toggleFavorite: 'CommandOrControl+D',
            copySelected: 'Enter',
            deleteSelected: 'Delete',
            search: 'CommandOrControl+F',
            navigateUp: 'ArrowUp',
            navigateDown: 'ArrowDown',
          },
          autoStart: false,
          showInSystemTray: true,
          maxHistoryLength: 100,
          ...initialPreferences,
        },
      },
    });
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockInvoke.mockResolvedValue({});
  });

  describe('Settings Persistence Workflow', () => {
    it('should load preferences on mount and save changes immediately', async () => {
      const mockPreferences: PreferencesState = {
        theme: 'dark',
        shortcuts: {
          toggleApp: 'CommandOrControl+Shift+V',
          clearHistory: 'CommandOrControl+Shift+Delete',
          toggleFavorite: 'CommandOrControl+D',
          copySelected: 'Enter',
          deleteSelected: 'Delete',
          search: 'CommandOrControl+F',
          navigateUp: 'ArrowUp',
          navigateDown: 'ArrowDown',
        },
        autoStart: false,
        showInSystemTray: true,
        maxHistoryLength: 100,
      };

      mockInvoke.mockResolvedValue(mockPreferences);
      const store = createStore();

      render(
        <Provider store={store}>
          <Settings />
        </Provider>
      );

      // Should load preferences on mount
      await waitFor(() => {
        expect(mockInvoke).toHaveBeenCalledWith('get_preferences');
      });

      // Toggle auto start
      const autoStartSwitch = screen.getByRole('checkbox', { name: /auto start/i });
      fireEvent.click(autoStartSwitch);

      // Should immediately call backend
      await waitFor(() => {
        expect(mockInvoke).toHaveBeenCalledWith('set_auto_start', { enabled: true });
      });

      await waitFor(() => {
        expect(mockInvoke).toHaveBeenCalledWith('save_preferences', {
          preferences: expect.objectContaining({
            autoStart: true,
          }),
        });
      });
    });

    it('should handle complete settings workflow with error recovery', async () => {
      const store = createStore();

      render(
        <Provider store={store}>
          <Settings />
        </Provider>
      );

      // First, simulate successful auto start toggle
      mockInvoke.mockResolvedValueOnce({});
      mockInvoke.mockResolvedValueOnce({});

      const autoStartSwitch = screen.getByRole('checkbox', { name: /auto start/i });
      fireEvent.click(autoStartSwitch);

      await waitFor(() => {
        expect(mockInvoke).toHaveBeenCalledWith('set_auto_start', { enabled: true });
      });

      // Now simulate error on system tray toggle
      mockInvoke.mockRejectedValueOnce(new Error('System tray error'));

      const systemTraySwitch = screen.getByRole('checkbox', { name: /show in system tray/i });
      fireEvent.click(systemTraySwitch);

      // Should attempt the call but handle error gracefully
      await waitFor(() => {
        expect(mockInvoke).toHaveBeenCalledWith('set_system_tray', { enabled: false });
      });

      // Settings should still be functional after error
      const maxHistoryInput = screen.getByDisplayValue('100');
      mockInvoke.mockResolvedValueOnce({});

      fireEvent.change(maxHistoryInput, { target: { value: '200' } });

      await waitFor(() => {
        expect(mockInvoke).toHaveBeenCalledWith('save_preferences', {
          preferences: expect.objectContaining({
            maxHistoryLength: 200,
          }),
        });
      });
    });
  });

  describe('Shortcut Management Integration', () => {
    it('should handle shortcut editing through dialog', async () => {
      const store = createStore();

      render(
        <Provider store={store}>
          <Settings />
        </Provider>
      );

      // Open shortcut dialog
      const editButtons = screen.getAllByTitle('Record shortcut');
      fireEvent.click(editButtons[0]); // Toggle App shortcut

      // Dialog should be visible
      expect(screen.getByTestId('shortcut-dialog')).toBeInTheDocument();
      expect(screen.getByText('Toggle App Shortcut')).toBeInTheDocument();

      // Save new shortcut
      mockInvoke.mockResolvedValueOnce({});
      mockInvoke.mockResolvedValueOnce({});

      const saveButton = screen.getByText('Save Shortcut');
      fireEvent.click(saveButton);

      // Should update shortcut and save
      await waitFor(() => {
        expect(mockInvoke).toHaveBeenCalledWith('save_preferences', {
          preferences: expect.objectContaining({
            shortcuts: expect.objectContaining({
              toggleApp: 'F1',
            }),
          }),
        });
      });

      await waitFor(() => {
        expect(mockInvoke).toHaveBeenCalledWith('update_global_shortcuts', {
          shortcuts: expect.objectContaining({
            toggleApp: 'F1',
          }),
        });
      });

      // Dialog should close
      expect(screen.queryByTestId('shortcut-dialog')).not.toBeInTheDocument();
    });

    it('should handle direct shortcut input changes', async () => {
      const store = createStore();

      render(
        <Provider store={store}>
          <Settings />
        </Provider>
      );

      mockInvoke.mockResolvedValue({});

      const toggleAppInput = screen.getByDisplayValue('CommandOrControl+Shift+V');
      fireEvent.change(toggleAppInput, { target: { value: 'CommandOrControl+Alt+V' } });

      // Should immediately save changes
      await waitFor(() => {
        expect(mockInvoke).toHaveBeenCalledWith('save_preferences', {
          preferences: expect.objectContaining({
            shortcuts: expect.objectContaining({
              toggleApp: 'CommandOrControl+Alt+V',
            }),
          }),
        });
      });

      await waitFor(() => {
        expect(mockInvoke).toHaveBeenCalledWith('update_global_shortcuts', {
          shortcuts: expect.objectContaining({
            toggleApp: 'CommandOrControl+Alt+V',
          }),
        });
      });
    });
  });

  describe('Bulk Operations', () => {
    it('should handle individual setting changes with auto-save', async () => {
      const store = createStore({
        autoStart: false,
        showInSystemTray: true,
        maxHistoryLength: 100,
      });

      render(
        <Provider store={store}>
          <Settings />
        </Provider>
      );

      mockInvoke.mockResolvedValue({});

      // Test auto start toggle
      const autoStartSwitch = screen.getByRole('checkbox', { name: /auto start/i });
      fireEvent.click(autoStartSwitch);

      await waitFor(() => {
        expect(mockInvoke).toHaveBeenCalledWith('set_auto_start', { enabled: true });
      });

      // Test max history length change
      const maxHistoryInput = screen.getByDisplayValue('100');
      fireEvent.change(maxHistoryInput, { target: { value: '250' } });

      await waitFor(() => {
        expect(mockInvoke).toHaveBeenCalledWith('save_preferences', {
          preferences: expect.objectContaining({
            maxHistoryLength: 250,
          }),
        });
      });

      // Should show success message for auto-save
      await waitFor(() => {
        expect(screen.getByText('Settings saved')).toBeInTheDocument();
      });
    });

    it('should handle reset to defaults operation', async () => {
      const store = createStore({
        autoStart: true,
        showInSystemTray: false,
        maxHistoryLength: 250,
      });

      const defaultPreferences: PreferencesState = {
        theme: 'dark',
        shortcuts: {
          toggleApp: 'CommandOrControl+Shift+V',
          clearHistory: 'CommandOrControl+Shift+Delete',
          toggleFavorite: 'CommandOrControl+D',
          copySelected: 'Enter',
          deleteSelected: 'Delete',
          search: 'CommandOrControl+F',
          navigateUp: 'ArrowUp',
          navigateDown: 'ArrowDown',
        },
        autoStart: false,
        showInSystemTray: true,
        maxHistoryLength: 100,
      };

      mockInvoke.mockResolvedValue(defaultPreferences);

      render(
        <Provider store={store}>
          <Settings />
        </Provider>
      );

      const resetButton = screen.getByRole('button', { name: /reset all settings/i });
      fireEvent.click(resetButton);

      await waitFor(() => {
        expect(mockInvoke).toHaveBeenCalledWith('get_preferences');
      });

      // UI should reflect reset values
      await waitFor(() => {
        expect(screen.getByRole('checkbox', { name: /auto start/i })).not.toBeChecked();
        expect(screen.getByRole('checkbox', { name: /show in system tray/i })).toBeChecked();
        expect(screen.getByDisplayValue('100')).toBeInTheDocument();
      });
    });
  });

  describe('Error Handling Integration', () => {
    it('should handle cascading errors gracefully', async () => {
      const store = createStore();

      render(
        <Provider store={store}>
          <Settings />
        </Provider>
      );

      // Simulate auto start failure
      mockInvoke.mockRejectedValueOnce(new Error('Auto start failed'));

      const autoStartSwitch = screen.getByRole('checkbox', { name: /auto start/i });
      fireEvent.click(autoStartSwitch);

      // Should handle error without crashing
      await waitFor(() => {
        expect(mockInvoke).toHaveBeenCalledWith('set_auto_start', { enabled: true });
      });

      // Other settings should still work
      mockInvoke.mockResolvedValue({});

      const maxHistoryInput = screen.getByDisplayValue('100');
      fireEvent.change(maxHistoryInput, { target: { value: '150' } });

      await waitFor(() => {
        expect(mockInvoke).toHaveBeenCalledWith('save_preferences', {
          preferences: expect.objectContaining({
            maxHistoryLength: 150,
          }),
        });
      });
    });

    it('should show error message for reset settings failures', async () => {
      const store = createStore();

      render(
        <Provider store={store}>
          <Settings />
        </Provider>
      );

      mockInvoke.mockRejectedValue(new Error('Reset failed'));

      const resetButton = screen.getByRole('button', { name: /reset all settings/i });
      fireEvent.click(resetButton);

      // The reset should fail silently or show an error in console
      await waitFor(() => {
        expect(mockInvoke).toHaveBeenCalledWith('get_preferences');
      });
    });
  });

  describe('State Synchronization', () => {
    it('should maintain UI state consistency during rapid changes', async () => {
      const store = createStore();

      render(
        <Provider store={store}>
          <Settings />
        </Provider>
      );

      mockInvoke.mockResolvedValue({});

      // Rapid changes
      const maxHistoryInput = screen.getByDisplayValue('100');
      
      fireEvent.change(maxHistoryInput, { target: { value: '200' } });
      fireEvent.change(maxHistoryInput, { target: { value: '300' } });
      fireEvent.change(maxHistoryInput, { target: { value: '400' } });

      // Should handle all changes
      await waitFor(() => {
        expect(screen.getByDisplayValue('400')).toBeInTheDocument();
      });

      // Should have made multiple save calls
      await waitFor(() => {
        expect(mockInvoke).toHaveBeenCalledWith('save_preferences', {
          preferences: expect.objectContaining({
            maxHistoryLength: 400,
          }),
        });
      });
    });

    it('should handle concurrent setting changes', async () => {
      const store = createStore();

      render(
        <Provider store={store}>
          <Settings />
        </Provider>
      );

      mockInvoke.mockResolvedValue({});

      // Simultaneous changes
      const autoStartSwitch = screen.getByRole('checkbox', { name: /auto start/i });
      const systemTraySwitch = screen.getByRole('checkbox', { name: /show in system tray/i });
      const maxHistoryInput = screen.getByDisplayValue('100');

      fireEvent.click(autoStartSwitch);
      fireEvent.click(systemTraySwitch);
      fireEvent.change(maxHistoryInput, { target: { value: '500' } });

      // All changes should be processed
      await waitFor(() => {
        expect(mockInvoke).toHaveBeenCalledWith('set_auto_start', { enabled: true });
        expect(mockInvoke).toHaveBeenCalledWith('set_system_tray', { enabled: false });
        expect(mockInvoke).toHaveBeenCalledWith('save_preferences', {
          preferences: expect.objectContaining({
            maxHistoryLength: 500,
          }),
        });
      });
    });
  });
});
