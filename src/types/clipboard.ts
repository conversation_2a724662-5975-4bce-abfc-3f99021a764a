export interface ClipboardEntry {
  id: string;
  content: string;
  type: 'text' | 'image' | 'file';
  timestamp: number;
  isFavorite: boolean;
  isPinned: boolean;
  name?: string; // User-defined name for the entry
  tags: string[]; // User-defined tags
  preview?: string; // For images or formatted text
  metadata?: {
    source?: string;
    size?: number;
    format?: string;
    colorCode?: string;
  };
}

export interface ClipboardState {
  history: ClipboardEntry[];
  favorites: ClipboardEntry[];
  maxHistoryLength: number;
  isMonitoring: boolean;
  searchQuery: string;
  selectedEntryId: string | null;
  selectedTags: string[];
}

export interface KeyboardShortcuts {
  toggleApp: string;
  clearHistory: string;
  toggleFavorite: string;
  copySelected: string;
  deleteSelected: string;
  search: string;
  navigateUp: string;
  navigateDown: string;
}

export interface PreferencesState {
  theme: 'dark' | 'light';
  shortcuts: KeyboardShortcuts;
  autoStart: boolean;
  showInSystemTray: boolean;
  maxHistoryLength: number;
}

export interface UIState {
  snackbar: {
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'warning' | 'info';
  };
}

export interface RootState {
  clipboard: ClipboardState;
  preferences: PreferencesState;
  ui: UIState;
}
