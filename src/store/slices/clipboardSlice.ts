import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { invoke } from '@tauri-apps/api/core';
import type { ClipboardEntry, ClipboardState } from '@/types/clipboard';

const initialState: ClipboardState = {
  history: [],
  favorites: [],
  maxHistoryLength: 100,
  isMonitoring: false,
  searchQuery: '',
  selectedEntryId: null,
  selectedTags: [],
};

// Async thunks for Tau<PERSON> commands
export const getClipboardHistory = createAsyncThunk(
  'clipboard/getHistory',
  async () => {
    const history = await invoke<ClipboardEntry[]>('get_clipboard_history');
    return history;
  }
);

export const saveClipboardEntry = createAsyncThunk(
  'clipboard/saveEntry',
  async (entry: Omit<ClipboardEntry, 'id' | 'timestamp'>) => {
    const savedEntry = await invoke<ClipboardEntry>('save_clipboard_entry', { entry });
    return savedEntry;
  }
);

export const toggleFavorite = createAsyncThunk(
  'clipboard/toggleFavorite',
  async (entryId: string) => {
    const updatedEntry = await invoke<ClipboardEntry>('toggle_favorite', { entryId });
    return updatedEntry;
  }
);

export const togglePin = createAsyncThunk(
  'clipboard/togglePin',
  async (entryId: string) => {
    const updatedEntry = await invoke<ClipboardEntry>('toggle_pin', { entryId });
    return updatedEntry;
  }
);

export const clearHistory = createAsyncThunk(
  'clipboard/clearHistory',
  async () => {
    await invoke('clear_clipboard_history');
  }
);

export const deleteEntry = createAsyncThunk(
  'clipboard/deleteEntry',
  async (entryId: string) => {
    await invoke('delete_clipboard_entry', { entryId });
    return entryId;
  }
);

export const startMonitoring = createAsyncThunk(
  'clipboard/startMonitoring',
  async () => {
    await invoke('start_clipboard_monitoring');
  }
);

export const stopMonitoring = createAsyncThunk(
  'clipboard/stopMonitoring',
  async () => {
    await invoke('stop_clipboard_monitoring');
  }
);

export const checkMonitoringStatus = createAsyncThunk(
  'clipboard/checkMonitoringStatus',
  async () => {
    const isMonitoring = await invoke<boolean>('is_monitoring_clipboard');
    return isMonitoring;
  }
);

export const updateEntryName = createAsyncThunk(
  'clipboard/updateEntryName',
  async ({ entryId, name }: { entryId: string; name: string }) => {
    const updatedEntry = await invoke<ClipboardEntry>('update_entry_name', { entryId, name });
    return updatedEntry;
  }
);

export const updateEntryTags = createAsyncThunk(
  'clipboard/updateEntryTags',
  async ({ entryId, tags }: { entryId: string; tags: string[] }) => {
    const updatedEntry = await invoke<ClipboardEntry>('update_entry_tags', { entryId, tags });
    return updatedEntry;
  }
);

// Helper function to trim history while preserving favorites
const trimHistoryPreservingFavorites = (history: ClipboardEntry[], maxLength: number): ClipboardEntry[] => {
  if (history.length <= maxLength) {
    return history;
  }

  // Separate favorites from non-favorites
  const favorites = history.filter(entry => entry.isFavorite);
  const nonFavorites = history.filter(entry => !entry.isFavorite);

  // Keep only the most recent non-favorites up to maxLength
  const trimmedNonFavorites = nonFavorites.slice(0, maxLength);

  // Combine and sort by timestamp (most recent first)
  const combined = [...favorites, ...trimmedNonFavorites];
  return combined.sort((a, b) => b.timestamp - a.timestamp);
};

const clipboardSlice = createSlice({
  name: 'clipboard',
  initialState,
  reducers: {
    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.searchQuery = action.payload;
    },
    setSelectedEntry: (state, action: PayloadAction<string | null>) => {
      state.selectedEntryId = action.payload;
    },
    addEntry: (state, action: PayloadAction<ClipboardEntry>) => {
      // Add to beginning of history
      state.history.unshift(action.payload);

      // Trim history while preserving favorites
      state.history = trimHistoryPreservingFavorites(state.history, state.maxHistoryLength);
    },
    updateMaxHistoryLength: (state, action: PayloadAction<number>) => {
      state.maxHistoryLength = action.payload;

      // Trim current history while preserving favorites
      state.history = trimHistoryPreservingFavorites(state.history, action.payload);
    },
    syncMaxHistoryLength: (state, action: PayloadAction<number>) => {
      state.maxHistoryLength = action.payload;
    },
    setSelectedTags: (state, action: PayloadAction<string[]>) => {
      state.selectedTags = action.payload;
    },
    toggleSelectedTag: (state, action: PayloadAction<string>) => {
      const tag = action.payload;
      const index = state.selectedTags.indexOf(tag);
      if (index === -1) {
        state.selectedTags.push(tag);
      } else {
        state.selectedTags.splice(index, 1);
      }
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getClipboardHistory.fulfilled, (state, action) => {
        const payload = Array.isArray(action.payload) ? action.payload : [];
        state.history = payload;
        state.favorites = payload.filter(entry => entry.isFavorite);
      })
      .addCase(saveClipboardEntry.fulfilled, (state, action) => {
        state.history.unshift(action.payload);
        // Trim history while preserving favorites
        state.history = trimHistoryPreservingFavorites(state.history, state.maxHistoryLength);
      })
      .addCase(toggleFavorite.fulfilled, (state, action) => {
        const entry = action.payload;

        if (!entry) return;

        // Update in history
        const historyIndex = state.history.findIndex(item => item.id === entry.id);
        if (historyIndex !== -1) {
          state.history[historyIndex] = entry;
        }

        // Update favorites list
        if (entry.isFavorite) {
          const existingIndex = state.favorites.findIndex(item => item.id === entry.id);
          if (existingIndex === -1) {
            state.favorites.push(entry);
          } else {
            state.favorites[existingIndex] = entry;
          }
        } else {
          state.favorites = state.favorites.filter(item => item.id !== entry.id);
        }
      })
      .addCase(togglePin.fulfilled, (state, action) => {
        const entry = action.payload;

        if (!entry) return;

        // Update in history
        const historyIndex = state.history.findIndex(item => item.id === entry.id);
        if (historyIndex !== -1) {
          state.history[historyIndex] = entry;
        }

        // Update in favorites if it exists there
        const favoritesIndex = state.favorites.findIndex(item => item.id === entry.id);
        if (favoritesIndex !== -1) {
          state.favorites[favoritesIndex] = entry;
        }
      })
      .addCase(clearHistory.fulfilled, (state) => {
        // Preserve favorited items when clearing history
        state.history = state.history.filter(entry => entry.isFavorite);
        state.selectedEntryId = null;
      })
      .addCase(deleteEntry.fulfilled, (state, action) => {
        const entryId = action.payload;
        state.history = state.history.filter(entry => entry.id !== entryId);
        state.favorites = state.favorites.filter(entry => entry.id !== entryId);
        
        if (state.selectedEntryId === entryId) {
          state.selectedEntryId = null;
        }
      })
      .addCase(startMonitoring.fulfilled, (state) => {
        state.isMonitoring = true;
      })
      .addCase(stopMonitoring.fulfilled, (state) => {
        state.isMonitoring = false;
      })
      .addCase(checkMonitoringStatus.fulfilled, (state, action) => {
        state.isMonitoring = action.payload;
      })
      .addCase(updateEntryName.fulfilled, (state, action) => {
        const entry = action.payload;
        
        if (!entry) return;
        
        // Update in history
        const historyIndex = state.history.findIndex(item => item.id === entry.id);
        if (historyIndex !== -1) {
          state.history[historyIndex] = entry;
        }
        
        // Update in favorites if it exists there
        const favoritesIndex = state.favorites.findIndex(item => item.id === entry.id);
        if (favoritesIndex !== -1) {
          state.favorites[favoritesIndex] = entry;
        }
      })
      .addCase(updateEntryTags.fulfilled, (state, action) => {
        const entry = action.payload;
        
        if (!entry) return;
        
        // Update in history
        const historyIndex = state.history.findIndex(item => item.id === entry.id);
        if (historyIndex !== -1) {
          state.history[historyIndex] = entry;
        }
        
        // Update in favorites if it exists there
        const favoritesIndex = state.favorites.findIndex(item => item.id === entry.id);
        if (favoritesIndex !== -1) {
          state.favorites[favoritesIndex] = entry;
        }
      });
  },
});

export const {
  setSearchQuery,
  setSelectedEntry,
  addEntry,
  updateMaxHistoryLength,
  syncMaxHistoryLength,
  setSelectedTags,
  toggleSelectedTag,
} = clipboardSlice.actions;

export default clipboardSlice.reducer;
