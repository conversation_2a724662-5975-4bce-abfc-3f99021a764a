use serde::{Deserialize, Serialize};
use std::sync::{Arc, Mutex};
use std::path::PathBuf;
use std::fs;
use uuid::Uuid;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ClipboardEntry {
    pub id: String,
    pub content: String,
    #[serde(rename = "type")]
    pub r#type: String, // "text", "image", "file"
    pub timestamp: u64,
    #[serde(rename = "isFavorite")]
    pub is_favorite: bool,
    #[serde(default)] // Add this
    #[serde(rename = "isPinned")]
    pub is_pinned: bool,
    pub name: Option<String>,
    pub tags: Vec<String>,
    pub preview: Option<String>,
    pub metadata: Option<ClipboardMetadata>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ClipboardMetadata {
    pub source: Option<String>,
    pub size: Option<u64>,
    pub format: Option<String>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct KeyboardShortcuts {
    pub toggle_app: String,
    pub clear_history: String,
    pub toggle_favorite: String,
    pub copy_selected: String,
    pub delete_selected: String,
    pub search: String,
    pub navigate_up: String,
    pub navigate_down: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Preferences {
    pub theme: String,
    pub shortcuts: KeyboardShortcuts,
    pub auto_start: bool,
    pub show_in_system_tray: bool,
    pub max_history_length: usize,
}

impl Default for Preferences {
    fn default() -> Self {
        Self {
            theme: "dark".to_string(),
            shortcuts: KeyboardShortcuts {
                toggle_app: "CommandOrControl+Shift+V".to_string(),
                clear_history: "CommandOrControl+Shift+Delete".to_string(),
                toggle_favorite: "CommandOrControl+D".to_string(),
                copy_selected: "Enter".to_string(),
                delete_selected: "Delete".to_string(),
                search: "CommandOrControl+F".to_string(),
                navigate_up: "ArrowUp".to_string(),
                navigate_down: "ArrowDown".to_string(),
            },
            auto_start: false,
            show_in_system_tray: true,
            max_history_length: 100,
        }
    }
}

pub struct ClipboardManager {
    pub history: Arc<Mutex<Vec<ClipboardEntry>>>,
    pub preferences: Arc<Mutex<Preferences>>,
    pub is_monitoring: Arc<Mutex<bool>>,
    data_dir: PathBuf,
}

impl ClipboardManager {
    pub fn new() -> Self {
        let data_dir = Self::get_data_dir();
        
        // Ensure data directory exists
        if let Err(e) = fs::create_dir_all(&data_dir) {
            eprintln!("Failed to create data directory: {}", e);
        }

        let mut manager = Self {
            history: Arc::new(Mutex::new(Vec::new())),
            preferences: Arc::new(Mutex::new(Preferences::default())),
            is_monitoring: Arc::new(Mutex::new(false)),
            data_dir,
        };

        // Load existing data
        if let Err(e) = manager.load_data() {
            eprintln!("Failed to load existing data: {}", e);
        }

        manager
    }

    fn get_data_dir() -> PathBuf {
        if let Some(config_dir) = dirs::config_dir() {
            config_dir.join("paste-king")
        } else {
            PathBuf::from(".paste-king")
        }
    }

    fn get_history_file(&self) -> PathBuf {
        self.data_dir.join("history.json")
    }

    fn get_preferences_file(&self) -> PathBuf {
        self.data_dir.join("preferences.json")
    }

    fn load_data(&mut self) -> Result<(), String> {
        // Load history
        if let Ok(history_data) = fs::read_to_string(self.get_history_file()) {
            if let Ok(history) = serde_json::from_str::<Vec<ClipboardEntry>>(&history_data) {
                if let Ok(mut current_history) = self.history.lock() {
                    *current_history = history;
                }
            }
        }

        // Load preferences
        if let Ok(prefs_data) = fs::read_to_string(self.get_preferences_file()) {
            if let Ok(preferences) = serde_json::from_str::<Preferences>(&prefs_data) {
                if let Ok(mut current_prefs) = self.preferences.lock() {
                    *current_prefs = preferences;
                }
            }
        }

        Ok(())
    }

    fn save_history(&self) -> Result<(), String> {
        let history = self.history.lock().map_err(|e| e.to_string())?;
        let json = serde_json::to_string_pretty(&*history).map_err(|e| e.to_string())?;
        fs::write(self.get_history_file(), json).map_err(|e| e.to_string())?;
        Ok(())
    }

    fn save_preferences(&self) -> Result<(), String> {
        let preferences = self.preferences.lock().map_err(|e| e.to_string())?;
        let json = serde_json::to_string_pretty(&*preferences).map_err(|e| e.to_string())?;
        fs::write(self.get_preferences_file(), json).map_err(|e| e.to_string())?;
        Ok(())
    }

    pub fn add_entry(&self, content: String, entry_type: String) -> Result<ClipboardEntry, String> {
        let mut history = self.history.lock().map_err(|e| e.to_string())?;
        let preferences = self.preferences.lock().map_err(|e| e.to_string())?;

        // Check if content already exists at the top of history
        if let Some(latest) = history.first() {
            if latest.content == content {
                return Ok(latest.clone());
            }
        }

        let entry = ClipboardEntry {
            id: Uuid::new_v4().to_string(),
            content,
            r#type: entry_type,
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_millis() as u64,
            is_favorite: false,
            is_pinned: false,
            name: None,
            tags: Vec::new(),
            preview: None,
            metadata: None,
        };

        // Add to beginning of history
        history.insert(0, entry.clone());

        // Trim history if it exceeds max length, but preserve favorited items
        self.trim_history_preserving_favorites(&mut history, preferences.max_history_length);

        // Release the lock before saving
        drop(history);
        drop(preferences);

        // Save to disk
        if let Err(e) = self.save_history() {
            eprintln!("Failed to save history: {}", e);
        }

        Ok(entry)
    }

    /// Helper method to trim history while preserving favorited items
    /// Favorites don't count toward the max_history_length limit
    fn trim_history_preserving_favorites(&self, history: &mut Vec<ClipboardEntry>, max_length: usize) {
        if history.len() <= max_length {
            return;
        }

        // Separate favorites from non-favorites
        let mut favorites = Vec::new();
        let mut non_favorites = Vec::new();

        for entry in history.drain(..) {
            if entry.is_favorite {
                favorites.push(entry);
            } else {
                non_favorites.push(entry);
            }
        }

        // Keep only the most recent non-favorites up to max_length
        non_favorites.truncate(max_length);

        // Combine favorites and non-favorites, maintaining chronological order
        // Favorites are preserved regardless of their position in history
        let mut combined = Vec::new();
        let mut fav_iter = favorites.into_iter().peekable();
        let mut non_fav_iter = non_favorites.into_iter().peekable();

        // Merge by timestamp (most recent first)
        while fav_iter.peek().is_some() || non_fav_iter.peek().is_some() {
            match (fav_iter.peek(), non_fav_iter.peek()) {
                (Some(fav), Some(non_fav)) => {
                    if fav.timestamp >= non_fav.timestamp {
                        combined.push(fav_iter.next().unwrap());
                    } else {
                        combined.push(non_fav_iter.next().unwrap());
                    }
                }
                (Some(_), None) => combined.push(fav_iter.next().unwrap()),
                (None, Some(_)) => combined.push(non_fav_iter.next().unwrap()),
                (None, None) => break,
            }
        }

        *history = combined;
    }

    pub fn get_history(&self) -> Result<Vec<ClipboardEntry>, String> {
        let history = self.history.lock().map_err(|e| e.to_string())?;
        Ok(history.clone())
    }

    pub fn toggle_favorite(&self, entry_id: &str) -> Result<ClipboardEntry, String> {
        let mut history = self.history.lock().map_err(|e| e.to_string())?;

        if let Some(entry) = history.iter_mut().find(|e| e.id == entry_id) {
            entry.is_favorite = !entry.is_favorite;
            let result = entry.clone();
            drop(history);

            // Save to disk
            if let Err(e) = self.save_history() {
                eprintln!("Failed to save history: {}", e);
            }

            Ok(result)
        } else {
            Err("Entry not found".to_string())
        }
    }

    pub fn toggle_pin(&self, entry_id: &str) -> Result<ClipboardEntry, String> {
        let mut history = self.history.lock().map_err(|e| e.to_string())?;

        if let Some(entry) = history.iter_mut().find(|e| e.id == entry_id) {
            entry.is_pinned = !entry.is_pinned;
            let result = entry.clone();
            drop(history);

            // Save to disk
            if let Err(e) = self.save_history() {
                eprintln!("Failed to save history: {}", e);
            }

            Ok(result)
        } else {
            Err("Entry not found".to_string())
        }
    }

    pub fn delete_entry(&self, entry_id: &str) -> Result<(), String> {
        let mut history = self.history.lock().map_err(|e| e.to_string())?;
        history.retain(|entry| entry.id != entry_id);
        drop(history);
        
        // Save to disk
        if let Err(e) = self.save_history() {
            eprintln!("Failed to save history: {}", e);
        }
        
        Ok(())
    }

    pub fn clear_history(&self) -> Result<(), String> {
        let mut history = self.history.lock().map_err(|e| e.to_string())?;

        // Preserve favorited items when clearing history
        history.retain(|entry| entry.is_favorite);

        drop(history);

        // Save to disk
        if let Err(e) = self.save_history() {
            eprintln!("Failed to save history: {}", e);
        }

        Ok(())
    }

    pub fn get_preferences(&self) -> Result<Preferences, String> {
        let preferences = self.preferences.lock().map_err(|e| e.to_string())?;
        Ok(preferences.clone())
    }

    pub fn update_preferences(&self, new_preferences: Preferences) -> Result<Preferences, String> {
        let mut preferences = self.preferences.lock().map_err(|e| e.to_string())?;

        // Update max history length and trim if necessary
        let old_max_length = preferences.max_history_length;
        let new_max_length = new_preferences.max_history_length;

        *preferences = new_preferences.clone();
        drop(preferences);

        // If max history length changed, trim history while preserving favorites
        if new_max_length != old_max_length {
            let mut history = self.history.lock().map_err(|e| e.to_string())?;
            self.trim_history_preserving_favorites(&mut history, new_max_length);
            drop(history);

            // Save history after trimming
            if let Err(e) = self.save_history() {
                eprintln!("Failed to save history after trimming: {}", e);
            }
        }
        
        // Save preferences to disk
        if let Err(e) = self.save_preferences() {
            eprintln!("Failed to save preferences: {}", e);
        }
        
        Ok(new_preferences)
    }

    pub fn set_monitoring(&self, monitoring: bool) -> Result<(), String> {
        let mut is_monitoring = self.is_monitoring.lock().map_err(|e| e.to_string())?;
        *is_monitoring = monitoring;
        Ok(())
    }

    pub fn is_monitoring(&self) -> Result<bool, String> {
        let is_monitoring = self.is_monitoring.lock().map_err(|e| e.to_string())?;
        Ok(*is_monitoring)
    }

    pub fn update_entry_name(&self, entry_id: &str, name: String) -> Result<ClipboardEntry, String> {
        let mut history = self.history.lock().map_err(|e| e.to_string())?;
        
        if let Some(entry) = history.iter_mut().find(|e| e.id == entry_id) {
            entry.name = if name.trim().is_empty() { None } else { Some(name) };
            let result = entry.clone();
            drop(history);
            
            // Save to disk
            if let Err(e) = self.save_history() {
                eprintln!("Failed to save history: {}", e);
            }
            
            Ok(result)
        } else {
            Err("Entry not found".to_string())
        }
    }

    pub fn update_entry_tags(&self, entry_id: &str, tags: Vec<String>) -> Result<ClipboardEntry, String> {
        let mut history = self.history.lock().map_err(|e| e.to_string())?;
        
        if let Some(entry) = history.iter_mut().find(|e| e.id == entry_id) {
            entry.tags = tags;
            let result = entry.clone();
            drop(history);
            
            // Save to disk
            if let Err(e) = self.save_history() {
                eprintln!("Failed to save history: {}", e);
            }
            
            Ok(result)
        } else {
            Err("Entry not found".to_string())
        }
    }
}
